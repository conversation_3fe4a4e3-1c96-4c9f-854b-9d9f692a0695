<template>
  <div class="test-result-container">
    <!-- 测试基本信息 -->
    <el-card class="test-info-card">
      <template #header>
        <div class="card-header">
          <span>测试信息</span>
          <el-tag
            :type="isSuccess ? 'success' : 'danger'"
            size="large"
          >
            {{ isSuccess ? '测试通过' : '测试失败' }}
          </el-tag>
        </div>
      </template>

      <el-descriptions :column="2" border>
        <el-descriptions-item label="接口名称">{{ interfaceConfig?.name }}</el-descriptions-item>
        <el-descriptions-item label="接口路径">{{ interfaceConfig?.path }}</el-descriptions-item>
        <el-descriptions-item label="HTTP方法">
          <el-tag :type="getMethodTagType(interfaceConfig?.method)">
            {{ interfaceConfig?.method }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="测试时间">{{ formatTestTime(currentTestRecord?.test_time) }}</el-descriptions-item>
        <el-descriptions-item label="状态码">
          <el-tag :type="currentTestRecord?.status_code >= 200 && currentTestRecord?.status_code < 300 ? 'success' : 'danger'">
            {{ currentTestRecord?.status_code || 'N/A' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="响应时间">{{ currentTestRecord?.response_time || 'N/A' }}ms</el-descriptions-item>
        <el-descriptions-item label="测试URL" :span="2">
          <el-input :value="currentTestRecord?.test_url" readonly size="small" />
        </el-descriptions-item>
        <el-descriptions-item label="数据条数">{{ currentTestRecord?.data_count || 0 }} 条</el-descriptions-item>
        <el-descriptions-item label="测试参数">{{ JSON.stringify(currentTestRecord?.test_params || {}) }}</el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 错误信息 -->
    <el-card v-if="!isSuccess && currentTestRecord?.error_message" class="error-card">
      <template #header>
        <span>错误信息</span>
      </template>
      <div class="error-content">
        <el-alert
          :title="currentTestRecord.error_message"
          type="error"
          :closable="false"
          show-icon
        />
      </div>
    </el-card>

    <!-- 响应数据 -->
    <el-card class="response-card">
      <template #header>
        <div class="card-header">
          <span>响应数据</span>
          <div class="header-actions">
            <el-button size="small" @click="copyResponseData">
              <el-icon><CopyDocument /></el-icon>
              复制
            </el-button>
            <el-button size="small" @click="formatResponseData">
              <el-icon><Document /></el-icon>
              格式化
            </el-button>
          </div>
        </div>
      </template>

      <div class="response-content">
        <el-input
          v-model="formattedResponseData"
          type="textarea"
          :rows="20"
          readonly
          class="response-textarea"
        />
      </div>
    </el-card>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <el-button @click="handleClose">关闭</el-button>
      <!-- 只在新测试模式下显示保存按钮 -->
      <el-button
        v-if="viewMode === 'new'"
        :type="isSuccess ? 'success' : 'warning'"
        @click="handleSaveTestResult"
        :loading="saving"
        :disabled="isSaved"
      >
        {{ isSaved ? '已保存' : (isSuccess ? '保存成功记录' : '保存失败记录') }}
      </el-button>
      <el-button type="primary" @click="handleRetest">重新测试</el-button>
      <el-button type="success" @click="handleDebug">调试接口</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { CopyDocument, Document } from '@element-plus/icons-vue';
import { useGlobalDrawerStore } from '@/stores/globalDrawerStore';
import { useGlobalDrawerMessenger } from '@/utils/piniaMessenger';
import type { InterfaceConfig } from '@/types/interface-config';
import { pageRefreshUtil } from '@/utils/pageRefreshUtil';

// 抽屉相关
const globalDrawerStore = useGlobalDrawerStore();
const drawerMessenger = useGlobalDrawerMessenger();

// 计算属性 - 从抽屉props中获取数据
const interfaceConfig = computed(() => globalDrawerStore.props.interfaceConfig);
const testRecord = computed(() => globalDrawerStore.props.testRecord);
const isSuccess = computed(() => globalDrawerStore.props.isSuccess || false);
const viewMode = computed(() => globalDrawerStore.props.viewMode || 'new'); // 'new' 或 'view'

// 响应数据
const formattedResponseData = ref('');

// 保存状态
const saving = ref(false);
const isSaved = ref(false);

// 加载状态
const loading = ref(false);
const loadedTestRecord = ref(null);

// 格式化测试时间
const formatTestTime = (timeStr: string) => {
  if (!timeStr) return 'N/A';
  try {
    return new Date(timeStr).toLocaleString('zh-CN');
  } catch {
    return timeStr;
  }
};

// 获取HTTP方法标签类型
const getMethodTagType = (method: string) => {
  const methodTypes: Record<string, string> = {
    'GET': 'success',
    'POST': 'primary',
    'PUT': 'warning',
    'DELETE': 'danger',
    'PATCH': 'info'
  };
  return methodTypes[method] || 'info';
};

// 格式化响应数据
const formatResponseData = () => {
  try {
    const record = currentTestRecord.value || testRecord.value;
    if (record?.response_data) {
      formattedResponseData.value = JSON.stringify(record.response_data, null, 2);
    } else {
      formattedResponseData.value = '无响应数据';
    }
  } catch (error) {
    formattedResponseData.value = '响应数据格式错误';
  }
};

// 复制响应数据
const copyResponseData = async () => {
  try {
    await navigator.clipboard.writeText(formattedResponseData.value);
    ElMessage.success('响应数据已复制到剪贴板');
  } catch (error) {
    ElMessage.error('复制失败');
  }
};

// 关闭抽屉
const handleClose = () => {
  drawerMessenger.hideDrawer();
};

// 重新测试
const handleRetest = () => {
  // 关闭当前抽屉
  drawerMessenger.hideDrawer();

  // 触发父组件重新测试
  try {
    // 通过window对象调用父组件的测试方法
    if ((window.parent as any)?.retestInterface) {
      (window.parent as any).retestInterface(interfaceConfig.value);
    } else {
      ElMessage.info('正在重新测试...');
    }
  } catch (error) {
    ElMessage.info('正在重新测试...');
  }
};

// 保存测试记录
const handleSaveTestResult = async () => {
  if (isSaved.value) {
    ElMessage.info('测试记录已保存');
    return;
  }

  saving.value = true;

  try {
    // 构造要保存的测试数据（包含完整的测试记录）
    const testDataToSave = {
      test_status: isSuccess.value ? 'success' : 'failed',
      last_test_at: testRecord.value.test_time || new Date().toISOString(),
      test_data: JSON.stringify(currentTestRecord.value) // 保存完整的测试记录到test_data字段
    };

    // 直接调用后端API保存测试状态
    const url = `http://${window.location.hostname}:8000/api/v1/interface/configs/${interfaceConfig.value.id}`;

    const response = await fetch(url, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testDataToSave)
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`保存失败: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();

    isSaved.value = true;
    const statusText = isSuccess.value ? '成功' : '失败';
    ElMessage.success(`测试${statusText}记录保存成功`);

    // 使用标准的页面刷新机制从后端重新获取数据（静默刷新，不显示消息）
    try {
      await pageRefreshUtil.triggerRefresh({
        pageKey: 'interfaceConfig',
        operation: 'edit',
        showMessage: false  // 静默刷新，不显示"接口配置已修改"消息
      });
    } catch (refreshError) {
      ElMessage.warning('刷新页面数据失败，请手动刷新页面');
    }

    // 保存成功后延迟关闭抽屉
    setTimeout(() => {
      drawerMessenger.hideDrawer();
    }, 1500);

  } catch (error) {
    ElMessage.error('保存测试记录失败');
  } finally {
    saving.value = false;
  }
};

// 调试接口
const handleDebug = () => {
  // 关闭抽屉
  drawerMessenger.hideDrawer();

  // 跳转到接口测试页面进行调试
  try {
    // 通过父窗口的tabStore添加接口测试页签
    const parentWindow = window.parent;
    if (parentWindow && (parentWindow as any).globalTabStore) {
      const tabStore = (parentWindow as any).globalTabStore;
      tabStore.addTab({
        id: 'interface-test',
        title: '接口测试',
        url: '/interface-test',
        icon: 'Cpu'
      });
      ElMessage.success('已跳转到接口测试页面');
    } else {
      ElMessage.info('即将跳转到接口调试页面...');
    }
  } catch (error) {
    ElMessage.info('即将跳转到接口调试页面...');
  }
};

// 加载已保存的测试记录
const loadSavedTestRecord = async () => {
  if (!interfaceConfig.value?.id) {
    return;
  }

  loading.value = true;
  try {

    // 调用API获取接口配置详情
    const response = await fetch(`http://${window.location.hostname}:8000/api/v1/interface/configs/${interfaceConfig.value.id}`);

    if (!response.ok) {
      throw new Error(`获取接口配置失败: ${response.status}`);
    }

    const configData = await response.json();

    if (configData.test_data) {
      try {
        const parsedTestData = JSON.parse(configData.test_data);
        loadedTestRecord.value = parsedTestData;

        // 格式化响应数据
        formatResponseData();

        ElMessage.success('测试记录加载成功');
      } catch (parseError) {
        ElMessage.error('测试记录数据格式错误');
      }
    } else {
      ElMessage.info('该接口暂无测试记录');
    }
  } catch (error) {
    ElMessage.error('加载测试记录失败');
  } finally {
    loading.value = false;
  }
};

// 获取当前显示的测试记录（优先使用加载的记录）
const currentTestRecord = computed(() => {
  return viewMode.value === 'view' && loadedTestRecord.value
    ? loadedTestRecord.value
    : testRecord.value;
});

// 初始化数据
onMounted(() => {
  if (viewMode.value === 'view') {
    // 查看模式：加载已保存的测试记录
    loadSavedTestRecord();
  } else {
    // 新测试模式：格式化当前测试数据
    formatResponseData();
  }
});
</script>

<style lang="scss" scoped>
.test-result-container {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.test-info-card {
  .el-descriptions {
    margin-top: 0;
  }
}

.error-card {
  .error-content {
    .el-alert {
      margin: 0;
    }
  }
}

.response-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  
  .response-content {
    flex: 1;
    
    .response-textarea {
      height: 100%;

      :deep(.el-textarea__inner) {
        font-family: 'Courier New', monospace;
        font-size: 12px;
        line-height: 1.4;
        resize: none;

        /* 标准化滚动条样式 */
        scrollbar-width: thin;
        scrollbar-color: #c1c1c1 #f1f1f1;

        &::-webkit-scrollbar {
          width: 8px;
          height: 8px;
        }

        &::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 4px;
        }

        &::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 4px;

          &:hover {
            background: #a8a8a8;
          }
        }
      }
    }
  }
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 20px;
  border-top: 1px solid #EBEEF5;
}
</style>
