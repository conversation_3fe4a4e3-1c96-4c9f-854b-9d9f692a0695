import type { MenuData } from '@/types/navigation';

/**
 * 菜单Mock数据
 * 
 * 说明：
 * - 这里包含系统的菜单结构和页面配置数据
 * - 将来菜单名称将从"系统管理-菜单设置"中动态获取
 * - 当前为静态数据，便于开发和测试
 */

export const mockMenuData: MenuData = {
  // 一级菜单数据
  topLevelMenus: [
    { 
      key: 'dashboard', 
      title: '首页看板', 
      icon: 'House', 
      children: [
        { name: 'dashboard-systemoverview', title: '系统总览' },
        { name: 'dashboard-servicemonitor', title: '服务监控' },
        { name: 'dashboard-useranalysis', title: '用户分析' },
        { name: 'dashboard-operationlogs', title: '运维日志' }
      ]
    },
    { 
      key: 'datasource', 
      title: '数据源管理', 
      icon: 'Files', 
      children: [
        { name: 'datasource-config', title: '业务数据源设置' }
      ]
    },
    { 
      key: 'interface', 
      title: '接口管理', 
      icon: 'Connection', 
      children: [
        { name: 'interface-group', title: '接口分组管理' },
        { name: 'interface-tag', title: '接口标签管理' },
        { name: 'interface-config', title: '接口配置管理' },
        { name: 'interface-test', title: '接口配置测试' }
      ]
    },
    { 
      key: 'client', 
      title: '客户端管理', 
      icon: 'User', 
      children: [
        { name: 'client-management', title: '客户端管理' },
        { name: 'permission-management', title: '权限管理' }
      ]
    },
    { 
      key: 'security', 
      title: '安全设置', 
      icon: 'Lock', 
      children: [
        { name: 'security-ip-control', title: 'IP访问控制' },
        { name: 'security-sql-whitelist', title: 'SQL白名单' },
        { name: 'security-flow-control', title: '流量控制' }
      ]
    },
    { 
      key: 'log', 
      title: '日志管理', 
      icon: 'Document', 
      children: [
        { name: 'log-access-log', title: '访问日志' },
        { name: 'log-error-log', title: '错误日志' },
        { name: 'log-log-settings', title: '日志设置' }
      ]
    },
    { 
      key: 'system', 
      title: '系统设置', 
      icon: 'Setting', 
      children: [
        { name: 'system-permission', title: '权限管理' },
        { name: 'system-notification', title: '通知管理' },
        { name: 'system-metadata', title: '元数据设置' },
        { name: 'system-menu', title: '菜单设置' }
      ]
    },
    { 
      key: 'task', 
      title: '任务管理', 
      icon: 'Clock', 
      children: [
        { name: 'task-monitor', title: '任务监控' },
        { name: 'task-scheduled', title: '定时任务' },
        { name: 'task-management', title: '任务管理' },
        { name: 'task-config', title: '任务配置' },
        { name: 'task-history', title: '任务历史' }
      ]
    }
  ],
  
  // 页签配置数据
  pageConfigs: [
    // 首页看板
    { name: 'dashboard-systemoverview', title: '系统总览', path: '/dashboard/SystemOverview', paneName: '' },
    { name: 'dashboard-servicemonitor', title: '服务监控', path: '/dashboard/ServiceMonitor', paneName: '' },
    { name: 'dashboard-useranalysis', title: '用户分析', path: '/dashboard/UserAnalysis', paneName: '' },
    { name: 'dashboard-operationlogs', title: '运维日志', path: '/dashboard/OperationLogs', paneName: '' },
    
    // 数据源管理
    { name: 'datasource-config', title: '业务数据源设置', path: '/datasource', paneName: '' },
    
    // 接口管理
    { name: 'interface-group', title: '接口分组管理', path: '/interface/group/list', paneName: '' },
    { name: 'interface-tag', title: '接口标签管理', path: '/interface/tag', paneName: '' },
    { name: 'interface-config', title: '接口配置管理', path: '/interface/config', paneName: '' },
    { name: 'interface-test', title: '接口配置测试', path: '/interface/test', paneName: '' },
    
    // 客户端管理
    { name: 'client-management', title: '客户端管理', path: '/client/ClientManagement', paneName: '' },
    { name: 'permission-management', title: '权限管理', path: '/client/PermissionManagement', paneName: '' },
    
    // 安全设置
    { name: 'security-ip-control', title: 'IP访问控制', path: '/security/SecurityConfig?tab=ip', paneName: '' },
    { name: 'security-sql-whitelist', title: 'SQL白名单', path: '/security/SecurityConfig?tab=sql', paneName: '' },
    { name: 'security-flow-control', title: '流量控制', path: '/security/SecurityConfig?tab=rate', paneName: '' },
    
    // 日志管理
    { name: 'log-access-log', title: '访问日志', path: '/log/LogManagement?tab=access', paneName: '' },
    { name: 'log-error-log', title: '错误日志', path: '/log/LogManagement?tab=error', paneName: '' },
    { name: 'log-log-settings', title: '日志设置', path: '/log/LogManagement?tab=settings', paneName: '' },
    
    // 系统设置
    { name: 'system-permission', title: '权限管理', path: '/system/Permissions?tab=roles', paneName: '' },
    { name: 'system-notification', title: '通知管理', path: '/system/Settings?tab=notification', paneName: '' },
    { name: 'system-metadata', title: '元数据设置', path: '/system/Settings?tab=basic', paneName: '' },
    { name: 'system-menu', title: '菜单设置', path: '/system/MenuSettings', paneName: '' },
    
    // 任务管理
    { name: 'task-monitor', title: '任务监控', path: '/task/TaskMonitor', paneName: '' },
    { name: 'task-scheduled', title: '定时任务', path: '/task/ScheduledTasks', paneName: '' },
    { name: 'task-management', title: '任务管理', path: '/task/TaskManagement', paneName: '' },
    { name: 'task-config', title: '任务配置', path: '/task/TaskConfig', paneName: '' },
    { name: 'task-history', title: '任务历史', path: '/task/TaskHistory', paneName: '' }
  ]
};
