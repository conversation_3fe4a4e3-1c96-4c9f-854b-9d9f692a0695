/**
 * 数据源相关类型定义
 */

// 数据库类型
export type DatabaseType = 'mysql' | 'postgresql' | 'sqlserver' | 'oracle' | 'sqlite';

// 数据源状态
export type DataSourceStatus = 'active' | 'inactive' | 'error';

// 数据源基本信息
export interface DataSource {
  id: number;
  name: string;           // 数据源名称
  description?: string;   // 数据源描述/备注
  dbType: DatabaseType;   // 数据库类型
  host: string;           // 主机地址
  port: number;           // 端口
  database: string;       // 数据库名
  username: string;       // 用户名
  password: string;       // 密码
  maxConnections: number; // 最大连接数
  connectionTimeout: number; // 连接超时时间(秒)
  refreshTime?: string;   // 定时刷新时间
  status: DataSourceStatus; // 状态
  createdAt: string;      // 创建时间
  updatedAt: string;      // 更新时间
  createdBy?: string;     // 创建人
}

// 数据源创建/更新请求
export interface DataSourceRequest {
  name: string;
  description?: string;   // 数据源描述/备注
  dbType: DatabaseType;
  host: string;
  port: number;
  database: string;
  username: string;
  password: string;
  maxConnections: number;
  connectionTimeout: number; // 连接超时时间(秒)
  refreshTime?: string;
  createdBy?: string;     // 创建人
}

// 数据源列表响应（分页）
export interface DataSourceListResponse {
  items: DataSource[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

// 数据源测试连接结果
export interface ConnectionTestResult {
  success: boolean;
  message: string;
  responseTime?: number;
  errorDetail?: string;
}

// 数据库类型选项
export interface DatabaseTypeOption {
  type: DatabaseType;
  name: string;
  icon: string;
  defaultPort: number;
}