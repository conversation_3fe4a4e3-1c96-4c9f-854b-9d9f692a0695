---
type: "always_apply"
---

# 前端开发规范

## 🎯 核心原则

### 1. 代码质量
- 所有代码必须通过 ESLint 检查
- 使用 TypeScript 严格模式
- 禁止使用 `any` 类型，优先使用具体类型或泛型
- 所有导出函数必须有 JSDoc 注释

### 2. 调试规范 ⚠️ 强制执行
- **禁止提交任何 console.log、console.debug、console.warn、console.error 调试代码**
- **禁止提交 alert()、debugger 等调试语句**
- **调试完成后必须清理所有调试信息**
- 生产环境问题排查使用专门的日志系统

## 📝 TypeScript/JavaScript 规范

### 1. 函数定义规范 ⚠️ 强制执行

**规则：禁止使用 `function` 关键字，必须使用箭头函数**

```typescript
// ❌ 错误：禁止使用 function 关键字
export function formatDate(time: number): string {
  return new Date(time).toLocaleDateString();
}

// ✅ 正确：使用箭头函数
export const formatDate = (time: number): string => {
  return new Date(time).toLocaleDateString();
};

// ✅ 正确：简化的箭头函数
const myFunction = (param: string) => param.toUpperCase();

// ✅ 正确：类方法保持原样（不受此规则影响）
class MyClass {
  async getData() {
    return await fetch('/api/data');
  }
}
```

### 2. ESLint 配置
```javascript
rules: {
  'func-style': ['error', 'expression', { 'allowArrowFunctions': true }],
  'prefer-arrow-callback': ['error', { 'allowNamedFunctions': false }],
  'no-var': 'error',
  'prefer-const': 'error',
  '@typescript-eslint/no-explicit-any': 'warn'
}
```

## 🎨 Vue 组件规范

### 1. 模板语法规范

```vue
<!-- ❌ 错误：模板字符串内使用双引号会导致语法冲突 -->
<EmptyState :description="`没有找到包含"${searchQuery}"的数据`" />

<!-- ✅ 正确：使用中文引号或单引号 -->
<EmptyState :description="`没有找到包含「${searchQuery}」的数据`" />
<EmptyState :description="`没有找到包含'${searchQuery}'的数据`" />
```

### 2. 空组件规范 ⚠️ 强制执行

**规则：所有数据列表页面必须添加空组件**

```vue
<el-table :data="tableData">
  <!-- 空状态 -->
  <template #empty>
    <el-empty description="暂无数据" :image-size="120">
      <template #description>
        <p>描述性文字</p>
        <p>引导性提示</p>
      </template>
      <el-button type="primary" @click="handleAction">
        <el-icon><Plus /></el-icon>
        操作按钮
      </el-button>
    </el-empty>
  </template>
</el-table>
```

### 3. 样式导入规范

```vue
<style lang="scss" scoped>
/* ❌ 禁止导入框架级样式 */
@import '@/assets/styles/framework.scss';

/* ✅ 正确：导入页面通用样式 */
@use '@/assets/styles/page-common.scss' as *;

/* ✅ 正确：导入组件专用样式 */
@import '@/assets/styles/component-name.scss';
</style>
```

## 🔄 数据转换规范

### 1. 前后端字段命名约定
- **前端：** 统一使用 camelCase (`ormModelConfig`, `parameterConfig`)
- **后端：** 统一使用 snake_case (`orm_model_config`, `parameter_config`)
- **转换：** 通过 `common-utils` 工具自动转换

### 2. API 数据转换

```typescript
// 字段转换工具导入
import { convertToCamelCase, convertToSnakeCase } from '@/utils/common-utils';

// API响应数据转换（自动）
return convertToCamelCase(data);

// API请求数据转换（自动）
body: JSON.stringify(convertToSnakeCase(data));
```

### 3. HTTP 客户端规范

```typescript
// ✅ 使用统一的 HTTP 客户端
import { apiClient } from '@/utils/http-client';

// 自动处理数据转换和错误处理
const response = await apiClient.get('/interface/configs');
const result = await apiClient.post('/interface/configs', data);
```

## 🔒 密码安全规范

### 1. 安全原则
- **编辑时不显示原密码**：出于安全考虑，编辑表单中密码字段始终为空
- **用户重新输入**：如需修改密码，用户必须重新输入
- **留空保持不变**：编辑时密码留空则保持原密码不变

### 2. 密码处理工具

```typescript
import { PasswordUtils } from '@/utils/common-utils';

// 安全处理数据源密码
const processedData = await PasswordUtils.processDataSourcePassword(data);
```

## 🏗️ 架构设计规范

### 1. 组件职责划分 ⚠️ 重要

**核心原则：**
- **组件**：负责UI渲染和用户交互的直接响应
- **组合式函数**：处理可复用的逻辑，不依赖具体组件
- **Pinia**：管理全局状态和跨组件的逻辑

**交互规则：**
```typescript
// ✅ 正确：组件调用Pinia和组合式函数
// Component.vue
const store = useMyStore();
const { someLogic } = useComposable();

// ❌ 错误：组合式函数与Pinia直接交互
// composable.ts
const store = useMyStore(); // 不允许

// ❌ 错误：Pinia之间直接交互
// store1.ts
const store2 = useStore2(); // 不允许
```

**Pinia使用规范：**
- Pinia完全是被调用的组件，不允许设置主动性工作
- 禁止在Pinia中自动获取数据、监听数据等
- 必须通过组件进行调用和交互

### 2. 组件导入规范 ⚠️ 强制执行

**Store导入规范：**
```typescript
// ✅ 正确：Store导入路径
import { useGlobalDrawerStore } from '@/stores/globalDrawerStore';
import { useTabContainerStore } from '@/stores/tabContainerStore';

// ❌ 错误：缺少Store后缀
import { useGlobalDrawerStore } from '@/stores/globalDrawer';
```

**抽屉组件规范：**
```typescript
// ✅ 正确：抽屉组件必须以Form结尾
InterfaceConfigForm.vue
DataSourceForm.vue

// ✅ 正确：抽屉使用方式
import { useGlobalDrawerMessenger } from '@/utils/piniaMessenger';
const drawerMessenger = useGlobalDrawerMessenger();

// 打开抽屉
drawerMessenger.showDrawer('InterfaceConfigForm', { id: 123 });

// 关闭抽屉
drawerMessenger.hideDrawer();
```

**组件注册：**
```typescript
// MainIndex.vue - 抽屉组件必须在此注册
(window as any).globalDrawerComponents = {
  DataSourceForm,
  InterfaceConfigForm,
  // 新增组件必须在此注册
};
```

## 📁 文件命名规范

### 1. 组件文件
- 页面组件：`PascalCase.vue`
- 表单组件：`PascalCaseForm.vue`
- 抽屉组件：`PascalCaseDrawer.vue`（必须以Form结尾）

### 2. 工具文件
- 服务文件：`kebab-case.service.ts`
- 工具文件：`kebab-case.ts`
- 类型文件：`kebab-case.ts`

### 3. 样式文件
- 页面样式：`page-common.scss`
- 组件样式：`component-name.scss`
- 框架样式：`framework.scss`（禁止修改）

## ✅ 检查清单

在提交代码前，请确保：

### 基础规范
- [ ] 所有函数都使用箭头函数语法
- [ ] 运行 `npm run lint:check` 无错误
- [ ] 所有类型注解完整
- [ ] 错误处理完善
- [ ] 注释清晰完整
- [ ] **无任何调试代码（console.log等）**

### 空组件规范
- [ ] 数据列表页面是否添加了空组件
- [ ] 是否使用了 `<template #empty>` 插槽
- [ ] 是否包含描述性文字和引导性提示
- [ ] 是否导入了所需图标
- [ ] 操作按钮是否与页面功能相关

### 数据转换规范
- [ ] 是否正确使用了字段转换工具
- [ ] API 调用是否使用统一的 HTTP 客户端
- [ ] 密码字段是否正确处理

## 🎨 Element Plus 组件规范

### 1. 表格滚动条规范 ⚠️ 重要

**问题：** Element Plus表格的滚动条样式和列头固定功能冲突

**解决方案：**
```scss
.table-wrapper {
  :deep(.el-table) {
    /* 自定义Element Plus表格的滚动条样式 */
    .el-table__body-wrapper {
      scrollbar-width: thin;
      scrollbar-color: rgba(157, 183, 189, 0.5) rgba(241, 245, 249, 0.3);

      &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }

      &::-webkit-scrollbar-track {
        background: rgba(241, 245, 249, 0.3);
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(157, 183, 189, 0.5);
        border-radius: 3px;
        transition: background 0.3s ease;
      }

      &::-webkit-scrollbar-thumb:hover {
        background: rgba(157, 183, 189, 0.8);
      }
    }

    /* 隐藏Element Plus的虚拟滚动条，使用原生滚动条 */
    .el-scrollbar__bar {
      display: none !important;
    }

    /* 确保表头固定 */
    .el-table__header-wrapper {
      position: sticky;
      top: 0;
      z-index: 10;
      background: #fff;
    }
  }
}
```

**规则要点：**
- 使用 `:height="tableHeight"` 启用固定高度模式
- 通过 `:deep()` 穿透组件样式边界
- 隐藏虚拟滚动条，保留原生滚动功能
- 确保表头固定功能正常工作

### 2. 表单输入框 Placeholder 规范

**问题：** 空字符串默认值导致placeholder不显示

**解决方案：**
```typescript
// ❌ 错误：使用空字符串
defaultValue: ''  // placeholder不会显示

// ✅ 正确：使用undefined
defaultValue: undefined  // placeholder正常显示

// 智能placeholder函数
const getPlaceholder = (row: any) => {
  if (row.type === 'header') {
    switch (row.name) {
      case 'Authorization': return 'Bearer your-token-here';
      case 'X-API-Key': return '请输入API密钥';
      default: return '请输入默认值';
    }
  }
  return '请输入默认值';
};
```

**规则要点：**
- 使用 `undefined` 而不是空字符串作为默认值
- 提供具体的、有意义的placeholder文本
- 根据字段类型和用途定制placeholder

## 💾 数据配置优化规范

### 1. 配置数据精简化 ⚠️ 重要

**原则：只记录有意义的信息，省略false值**

```typescript
// ❌ 错误：保存冗余的false值
const fieldConfig = {
  name: field.name,
  type: field.type,
  enabled: true,        // 冗余
  searchable: false,    // 冗余
  filterable: false,    // 冗余
  sortable: false       // 冗余
};

// ✅ 正确：只保存启用的功能
const fieldConfig: any = {
  name: field.name,
  type: field.type,
  comment: field.comment
};

// 只记录启用的功能
if (field.searchable) {
  fieldConfig.searchable = true;
}
if (field.filterable) {
  fieldConfig.filterable = true;
}
if (field.sortable) {
  fieldConfig.sortable = true;
  fieldConfig.sortOrder = field.sortOrder || 'desc';
}
```

**优化效果：**
- 减少40-50%的配置文件大小
- 提高配置可读性
- 降低维护成本
- 减少数据传输量

### 2. 向后兼容性处理

```typescript
// 加载时正确处理缺失的属性
searchable: ormField.searchable !== undefined ? ormField.searchable :
           (queryMapping?.fuzzySearchFields?.includes(ormField.name) || false)
```

## 🃏 卡片组件开发规范 ⚠️ 重要

### 1. 架构分离原则
- **公共组件**：BaseCard 放在 `src/components/common/` 目录下
- **自定义内容**：具体的卡片内容和样式放在 `src/views/` 层下面
- **职责分离**：BaseCard 负责布局和通用功能，业务组件负责具体内容展示

### 2. 组件使用规范

```vue
<BaseCard
  :item="dataSource"
  :config="{
    titleField: 'name',
    statusField: 'status',
    size: 'small',
    shadow: 'hover',
    style: 'compact-info'
  }"
  :show-default-header="false"
  :show-default-actions="false"
  @click="handleView(dataSource)"
  @edit="(item, event) => handleEdit(item, event)"
  @test="(item, event) => handleTest(item, event)"
  @delete="(item, event) => handleDelete(item, event)"
>
  <template #content="{ item }">
    <!-- 自定义卡片内容 -->
  </template>

  <template #actions="{ item }">
    <!-- 自定义操作按钮，必须传递 $event 参数 -->
    <el-button @click="handleEdit(item, $event)">编辑</el-button>
  </template>
</BaseCard>
```

### 3. 事件处理规范 ⚠️ 强制执行

```typescript
// ❌ 错误：未阻止事件冒泡
const handleEdit = (row: DataSource) => {
  // 编辑逻辑
};

// ✅ 正确：正确阻止事件冒泡
const handleEdit = (row: DataSource, event?: Event) => {
  if (event) {
    event.stopPropagation(); // 必须阻止事件冒泡
  }
  // 编辑逻辑
};

// ✅ 正确：模板中传递 $event 参数
<el-button @click="handleEdit(item, $event)">编辑</el-button>
```

### 4. 样式系统规范

**支持的卡片样式：**
- `compact-info`: 紧凑信息风格（唯一支持的样式）
  - 浅灰色背景 (#fafafa)
  - 简洁边框设计
  - 紧凑的内边距布局
  - 悬停时背景变为白色

**样式命名规范：**
```scss
// ✅ 正确：使用业务前缀避免冲突
.datasource-card-content {
  .card-header { /* 头部样式 */ }
  .connection-info { /* 连接信息样式 */ }
}

// ❌ 错误：与BaseCard样式冲突
.card-content { /* 避免使用通用类名 */ }
```

### 5. 插槽使用规范

```vue
<!-- 标准插槽结构 -->
<template #content="{ item }">
  <div class="business-card-content">
    <!-- 卡片头部：标题 + 状态 -->
    <div class="card-header">
      <h4 class="title">{{ item.name }}</h4>
      <el-tag :type="getStatusType(item.status)">
        {{ getStatusText(item.status) }}
      </el-tag>
    </div>

    <!-- 主要信息 -->
    <div class="main-info">
      <!-- 业务相关信息 -->
    </div>

    <!-- 底部状态栏 -->
    <div class="status-bar">
      <!-- 次要信息 -->
    </div>
  </div>
</template>
```

### 6. 检查清单

- [ ] 事件处理函数正确阻止冒泡
- [ ] 模板中操作按钮传递了 `$event` 参数
- [ ] 自定义样式使用了业务前缀
- [ ] 卡片配置包含必要的字段映射
- [ ] 插槽内容结构清晰合理

## 🔄 规则更新机制

**重要：当发现值得记录的新规则时，AI助手应自动更新此规则库**

更新原则：
1. 遇到重复性问题时，总结为规则
2. 发现最佳实践时，记录为标准
3. 修复重要bug时，提取为规范
4. 定期重构和优化规则库

示例更新场景：
- Element Plus组件使用规范
- 表格滚动条处理方案
- 表单输入框最佳实践
- 配置数据优化规则
- 卡片组件开发规范
- 性能优化规则
