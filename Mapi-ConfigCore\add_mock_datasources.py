# Copyright (c) 2025 左岚. All rights reserved.

import requests
import json

BASE_URL = "http://localhost:8000"

# 6个模拟数据源，使总数达到10个
mock_datasources = [
    {
        "name": "用户权限系统",
        "description": "用户权限管理系统数据库",
        "db_type": "postgres",
        "host": "*************",
        "port": 5432,
        "database": "user_auth_db",
        "username": "auth_admin",
        "password": "auth123456",
        "max_connections": 15,
        "connection_timeout": 45,
        "refresh_time": "01:30",
        "created_by": "system"
    },
    {
        "name": "财务管理系统",
        "description": "企业财务管理数据库",
        "db_type": "oracle",
        "host": "*************",
        "port": 1521,
        "database": "finance_db",
        "username": "finance_user",
        "password": "finance789",
        "max_connections": 25,
        "connection_timeout": 90,
        "refresh_time": "02:15",
        "created_by": "admin"
    },
    {
        "name": "库存管理系统",
        "description": "仓库库存管理数据库",
        "db_type": "mysql",
        "host": "*************",
        "port": 3306,
        "database": "inventory_db",
        "username": "inventory_admin",
        "password": "inv456789",
        "max_connections": 12,
        "connection_timeout": 60,
        "refresh_time": "03:00",
        "created_by": "system"
    },
    {
        "name": "客户关系管理",
        "description": "CRM客户关系管理系统",
        "db_type": "sqlserver",
        "host": "*************",
        "port": 1433,
        "database": "crm_database",
        "username": "crm_admin",
        "password": "crm123abc",
        "max_connections": 20,
        "connection_timeout": 75,
        "refresh_time": "04:30",
        "created_by": "admin"
    },
    {
        "name": "人力资源系统",
        "description": "HR人力资源管理数据库",
        "db_type": "postgres",
        "host": "*************",
        "port": 5432,
        "database": "hr_management",
        "username": "hr_admin",
        "password": "hr987654",
        "max_connections": 18,
        "connection_timeout": 50,
        "refresh_time": "05:00",
        "created_by": "system"
    },
    {
        "name": "产品目录系统",
        "description": "产品信息目录管理",
        "db_type": "mysql",
        "host": "*************",
        "port": 3306,
        "database": "product_catalog",
        "username": "product_admin",
        "password": "prod321654",
        "max_connections": 10,
        "connection_timeout": 40,
        "refresh_time": "06:15",
        "created_by": "admin"
    }
]

def add_datasources():
    """添加模拟数据源"""
    print("🚀 开始添加模拟数据源...")
    
    success_count = 0
    
    for i, datasource in enumerate(mock_datasources, 1):
        try:
            print(f"\n📝 添加第{i}个数据源: {datasource['name']}")
            
            response = requests.post(
                f"{BASE_URL}/datasource/",
                json=datasource,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 成功添加: ID={result.get('id', 'unknown')}")
                success_count += 1
            else:
                print(f"❌ 添加失败: {response.status_code}")
                print(f"   错误信息: {response.text}")
                
        except Exception as e:
            print(f"❌ 添加异常: {e}")
    
    print(f"\n🎉 完成！成功添加 {success_count}/{len(mock_datasources)} 个数据源")
    
    # 验证总数
    try:
        response = requests.get(f"{BASE_URL}/datasource/?page=1&size=20")
        if response.status_code == 200:
            result = response.json()
            total = result.get('total', 0)
            print(f"📊 当前数据源总数: {total}")
        else:
            print("❌ 无法获取数据源总数")
    except Exception as e:
        print(f"❌ 验证总数异常: {e}")

if __name__ == "__main__":
    add_datasources()
