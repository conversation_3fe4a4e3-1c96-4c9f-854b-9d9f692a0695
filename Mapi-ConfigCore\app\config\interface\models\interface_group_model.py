"""
接口分组数据模型
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, func
from app.shared.database import Base


class InterfaceGroupModel(Base):
    """接口分组表模型"""
    
    __tablename__ = "interface_groups"
    
    # 主键
    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    
    # 基本信息
    name = Column(String(100), nullable=False, unique=True, comment="分组名称")
    path_prefix = Column(String(50), nullable=False, unique=True, comment="路径前缀")
    description = Column(String(500), nullable=True, comment="分组描述")
    
    # 状态信息
    is_enabled = Column(Boolean, nullable=False, default=True, comment="是否启用")
    
    # 审计字段
    created_at = Column(DateTime, nullable=False, default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, nullable=False, default=func.now(), onupdate=func.now(), comment="更新时间")
    created_by = Column(String(50), nullable=True, comment="创建人")
    
    def __repr__(self):
        return f"<InterfaceGroup(id={self.id}, name='{self.name}', path_prefix='{self.path_prefix}')>"
    

