import type { 
  InterfaceTestRecord, 
  InterfaceTestRequest, 
  InterfaceTestResponse,
  InterfaceTestListResponse,
  InterfaceTestQuery,
  TestCase,
  TestCaseRequest,
  BatchTestRequest,
  BatchTestResponse,
  TestStatistics,
  InterfaceStatusStats
} from '@/types/interface-test';

/**
 * 接口测试 MOCK 数据
 * 用于：InterfaceTest.vue 接口测试页面
 */

/**
 * 模拟接口测试记录列表数据
 */
export const mockInterfaceTestRecords: InterfaceTestRecord[] = [
  {
    id: 1,
    interface_id: 1,
    interface_name: '项目列表查询',
    interface_path: '/api/project/list',
    interface_method: 'GET',
    test_name: '基础查询测试',
    test_params: { page: 1, page_size: 10, status: 'active' },
    test_headers: { 'Content-Type': 'application/json', 'Authorization': 'Bearer test-token' },
    response_status: 200,
    response_time: 145,
    response_data: {
      data: [
        { id: 1, name: '项目A', status: 'active', created_at: '2025-07-01' },
        { id: 2, name: '项目B', status: 'active', created_at: '2025-07-02' }
      ],
      total: 25,
      page: 1,
      page_size: 10
    },
    response_headers: { 'Content-Type': 'application/json', 'X-Response-Time': '145ms' },
    success: true,
    error_message: undefined,
    test_time: '2025-07-11 16:30:00',
    tester: 'admin'
  },
  {
    id: 2,
    interface_id: 1,
    interface_name: '项目列表查询',
    interface_path: '/api/project/list',
    interface_method: 'GET',
    test_name: '分页查询测试',
    test_params: { page: 2, page_size: 5 },
    test_headers: { 'Content-Type': 'application/json', 'Authorization': 'Bearer test-token' },
    response_status: 200,
    response_time: 132,
    response_data: {
      data: [
        { id: 6, name: '项目F', status: 'planning', created_at: '2025-07-06' },
        { id: 7, name: '项目G', status: 'completed', created_at: '2025-07-07' }
      ],
      total: 25,
      page: 2,
      page_size: 5
    },
    response_headers: { 'Content-Type': 'application/json', 'X-Response-Time': '132ms' },
    success: true,
    error_message: undefined,
    test_time: '2025-07-11 15:45:00',
    tester: 'admin'
  },
  {
    id: 3,
    interface_id: 2,
    interface_name: '项目详情查询',
    interface_path: '/api/project/{id}',
    interface_method: 'GET',
    test_name: '详情查询测试',
    test_params: { id: 1 },
    test_headers: { 'Content-Type': 'application/json', 'Authorization': 'Bearer test-token' },
    response_status: 200,
    response_time: 89,
    response_data: {
      id: 1,
      name: '项目A',
      status: 'active',
      description: '这是一个测试项目',
      budget: 100000,
      start_date: '2025-07-01',
      end_date: '2025-12-31',
      manager_name: '张三',
      team_members: ['李四', '王五', '赵六']
    },
    response_headers: { 'Content-Type': 'application/json', 'X-Response-Time': '89ms' },
    success: true,
    error_message: undefined,
    test_time: '2025-07-11 14:20:00',
    tester: 'admin'
  },
  {
    id: 4,
    interface_id: 2,
    interface_name: '项目详情查询',
    interface_path: '/api/project/{id}',
    interface_method: 'GET',
    test_name: '不存在ID测试',
    test_params: { id: 999 },
    test_headers: { 'Content-Type': 'application/json', 'Authorization': 'Bearer test-token' },
    response_status: 404,
    response_time: 45,
    response_data: { error: 'Project not found', code: 'PROJECT_NOT_FOUND' },
    response_headers: { 'Content-Type': 'application/json', 'X-Response-Time': '45ms' },
    success: false,
    error_message: '项目不存在',
    test_time: '2025-07-11 13:30:00',
    tester: 'admin'
  },
  {
    id: 5,
    interface_id: 3,
    interface_name: '项目创建',
    interface_path: '/api/project',
    interface_method: 'POST',
    test_name: '创建项目测试',
    test_params: {
      name: '测试项目',
      description: '这是一个通过API创建的测试项目',
      manager_id: 1,
      start_date: '2025-08-01'
    },
    test_headers: { 'Content-Type': 'application/json', 'Authorization': 'Bearer test-token' },
    response_status: 201,
    response_time: 234,
    response_data: {
      id: 26,
      name: '测试项目',
      status: 'planning',
      created_at: '2025-07-11 12:15:00'
    },
    response_headers: { 'Content-Type': 'application/json', 'Location': '/api/project/26' },
    success: true,
    error_message: undefined,
    test_time: '2025-07-11 12:15:00',
    tester: 'admin'
  },
  {
    id: 6,
    interface_id: 4,
    interface_name: '员工信息查询',
    interface_path: '/api/hr/employee/list',
    interface_method: 'GET',
    test_name: '员工列表查询',
    test_params: { department: '技术部', status: 'active' },
    test_headers: { 'Content-Type': 'application/json', 'Authorization': 'Bearer test-token' },
    response_status: 200,
    response_time: 167,
    response_data: {
      data: [
        { id: 1, name: '张三', department: '技术部', position: '高级工程师', email: '<EMAIL>' },
        { id: 2, name: '李四', department: '技术部', position: '工程师', email: '<EMAIL>' }
      ],
      total: 15,
      page: 1,
      page_size: 10
    },
    response_headers: { 'Content-Type': 'application/json', 'X-Response-Time': '167ms' },
    success: true,
    error_message: undefined,
    test_time: '2025-07-11 11:00:00',
    tester: 'hr_admin'
  },
  {
    id: 7,
    interface_id: 5,
    interface_name: '财务报表数据',
    interface_path: '/api/finance/report',
    interface_method: 'GET',
    test_name: '月度报表测试',
    test_params: { date_range: 'month', department: '技术部' },
    test_headers: { 'Content-Type': 'application/json', 'Authorization': 'Bearer test-token' },
    response_status: 500,
    response_time: 5000,
    response_data: { error: 'Database connection timeout', code: 'DB_TIMEOUT' },
    response_headers: { 'Content-Type': 'application/json', 'X-Response-Time': '5000ms' },
    success: false,
    error_message: '数据库连接超时',
    test_time: '2025-07-11 10:30:00',
    tester: 'finance_admin'
  },
  {
    id: 8,
    interface_id: 8,
    interface_name: '系统配置查询',
    interface_path: '/api/system/config',
    interface_method: 'GET',
    test_name: '系统配置查询',
    test_params: { category: 'email' },
    test_headers: { 'Content-Type': 'application/json', 'Authorization': 'Bearer test-token' },
    response_status: 200,
    response_time: 78,
    response_data: {
      data: [
        { config_key: 'smtp_host', config_value: 'smtp.company.com', description: 'SMTP服务器地址' },
        { config_key: 'smtp_port', config_value: '587', description: 'SMTP端口' }
      ]
    },
    response_headers: { 'Content-Type': 'application/json', 'X-Response-Time': '78ms' },
    success: true,
    error_message: undefined,
    test_time: '2025-07-11 09:15:00',
    tester: 'system_admin'
  }
];

/**
 * 模拟测试用例数据
 */
export const mockTestCases: TestCase[] = [
  {
    id: 1,
    interface_id: 1,
    name: '基础分页查询',
    description: '测试基本的分页查询功能',
    params: [
      { name: 'page', type: 'number', value: 1, description: '页码', required: false },
      { name: 'page_size', type: 'number', value: 10, description: '每页数量', required: false },
      { name: 'status', type: 'string', value: 'active', description: '项目状态', required: false }
    ],
    headers: { 'Content-Type': 'application/json', 'Authorization': 'Bearer test-token' },
    expected_status: 200,
    expected_fields: ['data', 'total', 'page', 'page_size'],
    is_active: true,
    created_at: '2025-07-01 09:00:00',
    updated_at: '2025-07-11 10:30:00',
    created_by: 'admin'
  },
  {
    id: 2,
    interface_id: 1,
    name: '状态筛选查询',
    description: '测试按状态筛选项目',
    params: [
      { name: 'status', type: 'string', value: 'completed', description: '项目状态', required: false }
    ],
    headers: { 'Content-Type': 'application/json', 'Authorization': 'Bearer test-token' },
    expected_status: 200,
    expected_fields: ['data', 'total'],
    is_active: true,
    created_at: '2025-07-01 09:30:00',
    updated_at: '2025-07-11 10:30:00',
    created_by: 'admin'
  },
  {
    id: 3,
    interface_id: 2,
    name: '正常ID查询',
    description: '使用有效ID查询项目详情',
    params: [
      { name: 'id', type: 'number', value: 1, description: '项目ID', required: true }
    ],
    headers: { 'Content-Type': 'application/json', 'Authorization': 'Bearer test-token' },
    expected_status: 200,
    expected_fields: ['id', 'name', 'status', 'description'],
    is_active: true,
    created_at: '2025-07-01 09:15:00',
    updated_at: '2025-07-10 16:45:00',
    created_by: 'admin'
  },
  {
    id: 4,
    interface_id: 2,
    name: '无效ID查询',
    description: '使用不存在的ID查询项目',
    params: [
      { name: 'id', type: 'number', value: 999, description: '不存在的项目ID', required: true }
    ],
    headers: { 'Content-Type': 'application/json', 'Authorization': 'Bearer test-token' },
    expected_status: 404,
    expected_fields: ['error', 'code'],
    is_active: true,
    created_at: '2025-07-01 09:45:00',
    updated_at: '2025-07-10 16:45:00',
    created_by: 'admin'
  }
];

/**
 * 模拟获取接口测试记录列表
 */
export const mockGetInterfaceTestRecords = async (
  page: number = 1,
  pageSize: number = 10,
  query?: InterfaceTestQuery
): Promise<InterfaceTestListResponse> => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 300));

  let filteredRecords = [...mockInterfaceTestRecords];

  // 接口筛选
  if (query?.interface_id) {
    filteredRecords = filteredRecords.filter(record => record.interface_id === query.interface_id);
  }

  // 搜索过滤
  if (query?.search) {
    const searchLower = query.search.toLowerCase();
    filteredRecords = filteredRecords.filter(record =>
      (record.test_name && record.test_name.toLowerCase().includes(searchLower)) ||
      (record.interface_name && record.interface_name.toLowerCase().includes(searchLower))
    );
  }

  // 成功状态筛选
  if (query?.success !== undefined) {
    filteredRecords = filteredRecords.filter(record => record.success === query.success);
  }

  // 测试人筛选
  if (query?.tester) {
    filteredRecords = filteredRecords.filter(record => record.tester === query.tester);
  }

  // 日期范围筛选
  if (query?.start_date || query?.end_date) {
    filteredRecords = filteredRecords.filter(record => {
      const testDate = new Date(record.test_time);
      if (query.start_date && testDate < new Date(query.start_date)) return false;
      if (query.end_date && testDate > new Date(query.end_date)) return false;
      return true;
    });
  }

  // 按测试时间倒序排序
  filteredRecords.sort((a, b) => new Date(b.test_time).getTime() - new Date(a.test_time).getTime());

  // 分页处理
  const total = filteredRecords.length;
  const start = (page - 1) * pageSize;
  const end = start + pageSize;
  const data = filteredRecords.slice(start, end);

  return {
    data,
    total,
    page,
    page_size: pageSize
  };
};

/**
 * 模拟执行接口测试
 */
export const mockExecuteInterfaceTest = async (
  request: InterfaceTestRequest
): Promise<InterfaceTestResponse> => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, Math.random() * 1000 + 200));

  const testTime = new Date().toISOString();
  const responseTime = Math.floor(Math.random() * 300 + 50);

  // 使用传入的测试结果，而不是随机生成
  const testResult: InterfaceTestResponse = {
    success: request.success !== undefined ? request.success : true,
    status_code: request.response_status || 200,
    response_time: request.response_time || responseTime,
    response_data: request.response_data || {
      message: '测试成功',
      data: { id: 1, name: '测试数据', status: 'success' },
      timestamp: testTime
    },
    response_headers: {
      'Content-Type': 'application/json',
      'X-Response-Time': `${responseTime}ms`
    },
    error_message: request.error_message || undefined,
    test_time: request.test_time || testTime
  };

  // 创建测试记录并保存到Mock数据中
  const newTestRecord: InterfaceTestRecord = {
    id: mockInterfaceTestRecords.length + 1,
    interface_id: request.interface_id,
    interface_name: request.interface_name || `接口${request.interface_id}`,
    interface_path: request.interface_path || `/api/interface/${request.interface_id}`,
    interface_method: request.interface_method || 'GET',
    test_name: request.test_name || `测试 - ${testTime}`,
    test_params: request.test_params || {},
    test_headers: request.test_headers || {},
    response_status: testResult.status_code,
    response_time: testResult.response_time,
    response_data: testResult.response_data,
    response_headers: testResult.response_headers,
    success: testResult.success,
    error_message: testResult.error_message,
    test_time: testResult.test_time,
    tester: request.tester || '系统自动测试'
  };

  // 先删除该接口的旧测试记录（实现覆盖效果）
  const existingIndex = mockInterfaceTestRecords.findIndex(
    record => record.interface_id === request.interface_id
  );
  if (existingIndex !== -1) {
    mockInterfaceTestRecords.splice(existingIndex, 1);
  }

  // 添加新的测试记录到数组开头（最新的在前面）
  mockInterfaceTestRecords.unshift(newTestRecord);

  console.log('🔍 保存测试记录:', newTestRecord);
  console.log('🔍 当前所有测试记录:', mockInterfaceTestRecords);

  return testResult;
};

/**
 * 模拟清除测试记录
 */
export const mockClearTestRecords = async (interfaceId?: number): Promise<boolean> => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 200));

  if (interfaceId) {
    // 删除指定接口的测试记录
    const initialLength = mockInterfaceTestRecords.length;
    for (let i = mockInterfaceTestRecords.length - 1; i >= 0; i--) {
      if (mockInterfaceTestRecords[i].interface_id === interfaceId) {
        mockInterfaceTestRecords.splice(i, 1);
      }
    }
    const deletedCount = initialLength - mockInterfaceTestRecords.length;
    console.log(`🔍 清除接口 ${interfaceId} 的测试记录，删除了 ${deletedCount} 条记录`);
  } else {
    // 清除所有测试记录
    const deletedCount = mockInterfaceTestRecords.length;
    mockInterfaceTestRecords.length = 0;
    console.log(`🔍 清除所有测试记录，删除了 ${deletedCount} 条记录`);
  }

  return true;
};

/**
 * 模拟批量测试接口
 */
export const mockBatchTestInterfaces = async (
  request: BatchTestRequest
): Promise<BatchTestResponse> => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 1000));

  const results: InterfaceTestResponse[] = [];
  let successCount = 0;
  let failedCount = 0;

  for (const interfaceId of request.interface_ids) {
    const testResult = await mockExecuteInterfaceTest({
      interface_id: interfaceId,
      test_params: {},
      test_headers: { 'Content-Type': 'application/json' }
    });

    results.push(testResult);

    if (testResult.success) {
      successCount++;
    } else {
      failedCount++;
    }
  }

  return {
    total: request.interface_ids.length,
    success_count: successCount,
    failed_count: failedCount,
    results
  };
};

/**
 * 模拟获取测试统计信息
 */
export const mockGetTestStatistics = async (): Promise<TestStatistics> => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 200));

  const totalTests = mockInterfaceTestRecords.length;
  const successTests = mockInterfaceTestRecords.filter(record => record.success).length;
  const failedTests = totalTests - successTests;
  const successRate = totalTests > 0 ? (successTests / totalTests) * 100 : 0;

  const avgResponseTime = totalTests > 0
    ? mockInterfaceTestRecords.reduce((sum, record) => sum + record.response_time, 0) / totalTests
    : 0;

  const lastTestTime = mockInterfaceTestRecords.length > 0
    ? mockInterfaceTestRecords.sort((a, b) => new Date(b.test_time).getTime() - new Date(a.test_time).getTime())[0].test_time
    : undefined;

  // 模拟今日和本周测试次数
  const today = new Date().toDateString();
  const todayTests = mockInterfaceTestRecords.filter(record =>
    new Date(record.test_time).toDateString() === today
  ).length;

  const oneWeekAgo = new Date();
  oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
  const thisWeekTests = mockInterfaceTestRecords.filter(record =>
    new Date(record.test_time) >= oneWeekAgo
  ).length;

  // 计算新增统计信息
  const responseTimes = mockInterfaceTestRecords.map(record => record.response_time);
  const fastestResponseTime = responseTimes.length > 0 ? Math.min(...responseTimes) : 0;
  const slowestResponseTime = responseTimes.length > 0 ? Math.max(...responseTimes) : 0;

  // 统计测试最多的接口
  const interfaceTestCounts = mockInterfaceTestRecords.reduce((acc, record) => {
    acc[record.interface_name || ''] = (acc[record.interface_name || ''] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const mostTestedInterface = Object.keys(interfaceTestCounts).length > 0
    ? Object.keys(interfaceTestCounts).reduce((a, b) => interfaceTestCounts[a] > interfaceTestCounts[b] ? a : b)
    : undefined;

  // 模拟趋势（基于最近的测试数据）
  const recentTrend: 'up' | 'down' | 'stable' = thisWeekTests > todayTests * 3 ? 'up' :
                                                thisWeekTests < todayTests ? 'down' : 'stable';

  return {
    total_tests: totalTests,
    success_tests: successTests,
    failed_tests: failedTests,
    success_rate: Math.round(successRate * 100) / 100,
    avg_response_time: Math.round(avgResponseTime),
    last_test_time: lastTestTime,
    today_tests: todayTests,
    this_week_tests: thisWeekTests,

    // 新增统计信息
    total_interfaces: 8, // 总接口数量
    total_groups: 4, // 总分组数量（API管理、用户管理、项目管理、系统管理）
    tested_interfaces: 6, // 已测试接口数
    untested_interfaces: 2, // 未测试接口数
    fastest_response_time: fastestResponseTime,
    slowest_response_time: slowestResponseTime,
    most_tested_interface: mostTestedInterface,
    recent_test_trend: recentTrend
  };
};

/**
 * 模拟获取接口状态统计
 */
export const mockGetInterfaceStatusStats = async (): Promise<InterfaceStatusStats> => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 200));

  // 基于接口配置Mock数据计算统计
  const totalInterfaces = 8; // 根据interface-config.mock.ts中的数据
  const enabledInterfaces = 7; // 启用的接口数
  const testedInterfaces = 6; // 已测试的接口数
  const successInterfaces = 5; // 测试成功的接口数
  const failedInterfaces = 1; // 测试失败的接口数
  const untestedInterfaces = totalInterfaces - testedInterfaces;

  return {
    total_interfaces: totalInterfaces,
    enabled_interfaces: enabledInterfaces,
    tested_interfaces: testedInterfaces,
    success_interfaces: successInterfaces,
    failed_interfaces: failedInterfaces,
    untested_interfaces: untestedInterfaces
  };
};
