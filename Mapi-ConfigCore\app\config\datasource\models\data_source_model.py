"""
数据源模型
SQLAlchemy ORM模型定义
"""

from sqlalchemy import Column, Integer, String, DateTime, func
from app.shared.database import Base

class DataSourceModel(Base):
    """数据源表模型"""
    
    __tablename__ = "data_sources"
    
    # 主键
    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    
    # 基本信息
    name = Column(String(100), nullable=False, unique=True, comment="数据源名称")
    description = Column(String(200), nullable=True, comment="数据源描述/备注")
    
    # 数据库连接信息
    db_type = Column(String(20), nullable=False, comment="数据库类型")
    host = Column(String(255), nullable=False, comment="主机地址")
    port = Column(Integer, nullable=False, comment="端口号")
    database = Column(String(100), nullable=False, comment="数据库名")
    username = Column(String(100), nullable=False, comment="用户名")
    password = Column(String(500), nullable=False, comment="密码(AES加密)")
    
    # 连接配置
    max_connections = Column(Integer, nullable=False, default=10, comment="最大连接数")
    connection_timeout = Column(Integer, nullable=False, default=60, comment="连接超时时间(秒)")
    refresh_time = Column(String(10), nullable=True, comment="定时刷新时间(HH:MM)")
    
    # 状态信息
    status = Column(String(20), nullable=False, default='active', comment="状态(active/inactive/error)")
    
    # 审计字段
    created_at = Column(DateTime, nullable=False, default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, nullable=False, default=func.now(), onupdate=func.now(), comment="更新时间")
    created_by = Column(String(50), nullable=True, comment="创建人")
    
    def __repr__(self):
        return f"<DataSource(id={self.id}, name='{self.name}', db_type='{self.db_type}')>"
    

