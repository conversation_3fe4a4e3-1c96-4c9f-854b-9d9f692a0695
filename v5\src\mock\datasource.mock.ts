import type { DataSource, ConnectionTestResult, DatabaseTypeOption } from '../types/datasource';

/**
 * 数据库类型选项
 */
export const databaseTypes: DatabaseTypeOption[] = [
  { type: 'mysql', name: 'MySQL', icon: '🐬', defaultPort: 3306 },
  { type: 'postgresql', name: 'PostgreSQL', icon: '🐘', defaultPort: 5432 },
  { type: 'sqlserver', name: 'SQL Server', icon: '🏢', defaultPort: 1433 },
  { type: 'oracle', name: 'Oracle', icon: '🔴', defaultPort: 1521 },
  { type: 'sqlite', name: 'SQLite', icon: '📁', defaultPort: 0 }
];

/**
 * 模拟数据源列表
 */
export const mockDataSources: DataSource[] = [
  {
    id: 1,
    name: '管理系统数据库',
    description: '项目管理系统的主数据库',
    dbType: 'sqlserver',
    host: '*************',
    port: 1433,
    database: 'ProjectManagement_DB',
    username: 'pm_admin',
    password: '********',
    maxConnections: 20,
    connectionTimeout: 60,
    refreshTime: '02:00',
    status: 'active',
    createdAt: '2025-07-01 14:30:00',
    updatedAt: '2025-07-11 09:45:00',
    createdBy: 'admin'
  },
  {
    id: 2,
    name: '财务管理数据库',
    description: '财务系统数据库连接',
    dbType: 'oracle',
    host: '*************',
    port: 1521,
    database: 'FINANCE_PROD',
    username: 'finance_user',
    password: '********',
    maxConnections: 30,
    connectionTimeout: 60,
    refreshTime: '01:30',
    status: 'active',
    createdAt: '2025-07-02 15:20:00',
    updatedAt: '2025-07-11 10:15:00',
    createdBy: 'admin'
  },
  {
    id: 3,
    name: '印章管理数据库',
    description: '印章管理系统数据库',
    dbType: 'mysql',
    host: '*************',
    port: 3306,
    database: 'seal_management',
    username: 'seal_admin',
    password: '********',
    maxConnections: 8,
    connectionTimeout: 60,
    refreshTime: '03:30',
    status: 'inactive',
    createdAt: '2025-07-03 10:30:00',
    updatedAt: '2025-07-09 11:45:00',
    createdBy: 'admin'
  },
  {
    id: 4,
    name: '办公自动化数据库',
    description: '办公自动化系统数据库',
    dbType: 'postgres',
    host: '*************',
    port: 5432,
    database: 'office_automation',
    username: 'oa_admin',
    password: '********',
    maxConnections: 15,
    connectionTimeout: 60,
    refreshTime: '04:00',
    status: 'active',
    createdAt: '2025-07-04 10:30:00',
    updatedAt: '2025-07-12 11:45:00',
    createdBy: 'admin'
  },
  {
    id: 5,
    name: '企业门户数据库',
    description: '企业门户网站数据库',
    dbType: 'sqlserver',
    host: '*************',
    port: 1433,
    database: 'Enterprise_Portal',
    username: 'portal_admin',
    password: '********',
    maxConnections: 25,
    connectionTimeout: 60,
    refreshTime: '05:30',
    status: 'error',
    createdAt: '2025-07-05 12:00:00',
    updatedAt: '2025-07-08 13:20:00',
    createdBy: 'admin'
  },
  {
    id: 6,
    name: '人力资源数据库',
    description: '人力资源管理系统',
    dbType: 'mysql',
    host: '*************',
    port: 3306,
    database: 'hr_management',
    username: 'hr_system',
    password: '********',
    maxConnections: 18,
    connectionTimeout: 60,
    refreshTime: '02:30',
    status: 'active',
    createdAt: '2025-07-06 14:15:00',
    updatedAt: '2025-07-13 15:30:00',
    createdBy: 'admin'
  },
  {
    id: 7,
    name: '客户关系管理',
    description: 'CRM客户管理系统',
    dbType: 'postgres',
    host: '*************',
    port: 5432,
    database: 'crm_system',
    username: 'crm_admin',
    password: '********',
    maxConnections: 20,
    connectionTimeout: 45,
    refreshTime: '03:00',
    status: 'active',
    createdAt: '2025-07-07 16:20:00',
    updatedAt: '2025-07-14 17:45:00',
    createdBy: 'admin'
  },
  {
    id: 8,
    name: '库存管理系统',
    description: '仓储库存管理数据库',
    dbType: 'mysql',
    host: '*************',
    port: 3306,
    database: 'inventory_mgmt',
    username: 'inventory',
    password: '********',
    maxConnections: 15,
    connectionTimeout: 30,
    refreshTime: '04:30',
    status: 'inactive',
    createdAt: '2025-07-08 11:30:00',
    updatedAt: '2025-07-15 12:15:00',
    createdBy: 'admin'
  },
  {
    id: 9,
    name: '财务报表中心',
    description: '财务数据分析系统',
    dbType: 'oracle',
    host: '*************',
    port: 1521,
    database: 'FINANCE_BI',
    username: 'finance_bi',
    password: '********',
    maxConnections: 12,
    connectionTimeout: 90,
    refreshTime: '01:00',
    status: 'active',
    createdAt: '2025-07-09 09:45:00',
    updatedAt: '2025-07-16 10:30:00',
    createdBy: 'finance_admin'
  },
  {
    id: 10,
    name: '日志监控系统',
    description: '系统日志收集分析',
    dbType: 'postgres',
    host: '*************',
    port: 5432,
    database: 'log_analytics',
    username: 'log_admin',
    password: '********',
    maxConnections: 8,
    connectionTimeout: 30,
    refreshTime: '06:00',
    status: 'active',
    createdAt: '2025-07-10 13:20:00',
    updatedAt: '2025-07-17 14:45:00',
    createdBy: 'ops_admin'
  },
  {
    id: 7,
    name: '合同管理数据库',
    description: '企业合同管理系统',
    dbType: 'oracle',
    host: '*************',
    port: 1521,
    database: 'CONTRACT_SYS',
    username: 'contract_admin',
    password: '********',
    maxConnections: 12,
    connectionTimeout: 45,
    refreshTime: '06:00',
    status: 'inactive',
    createdAt: '2025-07-07 16:00:00',
    updatedAt: '2025-07-14 16:30:00',
    createdBy: 'contract_admin'
  },
  {
    id: 8,
    name: '客户关系数据库',
    description: 'CRM客户管理系统',
    dbType: 'mysql',
    host: '*************',
    port: 3306,
    database: 'crm_system',
    username: 'crm_admin',
    password: '********',
    maxConnections: 20,
    connectionTimeout: 60,
    refreshTime: '03:00',
    status: 'active',
    createdAt: '2025-07-08 10:00:00',
    updatedAt: '2025-07-15 14:20:00',
    createdBy: 'admin'
  },
  {
    id: 9,
    name: '数据分析平台',
    description: '大数据分析与BI系统',
    dbType: 'postgres',
    host: '*************',
    port: 5432,
    database: 'analytics_db',
    username: 'analytics',
    password: '********',
    maxConnections: 25,
    connectionTimeout: 90,
    refreshTime: '01:00',
    status: 'active',
    createdAt: '2025-07-09 14:30:00',
    updatedAt: '2025-07-16 16:45:00',
    createdBy: 'data_admin'
  },
  {
    id: 10,
    name: '配置管理中心',
    description: '系统配置与参数管理',
    dbType: 'sqlite',
    host: '*************',
    port: 0,
    database: 'config_center.db',
    username: 'config',
    password: '********',
    maxConnections: 5,
    connectionTimeout: 20,
    refreshTime: '06:00',
    status: 'active',
    createdAt: '2025-07-10 09:00:00',
    updatedAt: '2025-07-17 11:30:00',
    createdBy: 'config_admin'
  }

];

/**
 * 模拟测试连接结果
 */
export const mockTestConnection = (dbType: string): ConnectionTestResult => {
  // 模拟80%成功率
  const isSuccess = Math.random() > 0.2;
  
  if (isSuccess) {
    let version = '';
    switch (dbType) {
      case 'mysql':
        version = 'MySQL 8.0.32';
        break;
      case 'postgres':
        version = 'PostgreSQL 15.1';
        break;
      case 'sqlserver':
        version = 'SQL Server 2022';
        break;
      case 'oracle':
        version = 'Oracle 19c';
        break;
      case 'sqlite':
        version = 'SQLite 3.40.1';
        break;
      default:
        version = '未知版本';
    }
    
    return {
      success: true,
      message: '连接测试成功'
    };
  } else {
    return {
      success: false,
      message: '连接测试失败',
      errorDetail: '无法连接到数据库服务器，请检查连接信息是否正确。'
    };
  }
};