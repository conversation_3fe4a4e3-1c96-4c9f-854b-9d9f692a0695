#!/usr/bin/env python3
# Copyright (c) 2025 左岚. All rights reserved.
"""
直接在数据库中添加5个新的数据源记录
"""

import sys
import sqlite3
from pathlib import Path
from datetime import datetime
import os

# 添加项目路径到sys.path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.shared.crypto_utils import encrypt_password

def get_db_path():
    """获取数据库文件路径"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(os.path.dirname(current_dir))
    db_path = os.path.join(project_root, "mapi-data", "mapi_config.db")
    return db_path

def add_datasources():
    """添加5个新的数据源记录"""
    print("🚀 开始添加数据源记录...")
    
    # 准备5个新的数据源数据
    new_datasources = [
        {
            "name": "客户关系管理系统",
            "description": "CRM客户关系管理与销售跟踪系统",
            "db_type": "sqlite",
            "host": "/data/crm/crm_database.db",
            "port": 0,
            "database": "crm_database",
            "username": "crm_admin",
            "password": "crm_pass123",
            "max_connections": 5,
            "connection_timeout": 60,
            "refresh_time": "02:30",
            "status": "active"
        },
        {
            "name": "物流管理系统",
            "description": "物流配送与仓储管理系统",
            "db_type": "mysql",
            "host": "*************",
            "port": 3306,
            "database": "logistics_system",
            "username": "logistics_admin",
            "password": "logistics_pass456",
            "max_connections": 15,
            "connection_timeout": 60,
            "refresh_time": "01:00",
            "status": "active"
        },
        {
            "name": "生产管理系统",
            "description": "制造业生产计划与质量管理系统",
            "db_type": "postgresql",
            "host": "*************",
            "port": 5432,
            "database": "production_system",
            "username": "production_admin",
            "password": "production_pass789",
            "max_connections": 20,
            "connection_timeout": 90,
            "refresh_time": "03:00",
            "status": "active"
        },
        {
            "name": "营销分析系统",
            "description": "市场营销数据分析与报表系统",
            "db_type": "oracle",
            "host": "*************",
            "port": 1521,
            "database": "marketing_db",
            "username": "marketing_admin",
            "password": "marketing_pass321",
            "max_connections": 12,
            "connection_timeout": 120,
            "refresh_time": "04:00",
            "status": "active"
        },
        {
            "name": "供应链管理系统",
            "description": "供应商管理与采购流程系统",
            "db_type": "sqlserver",
            "host": "*************",
            "port": 1433,
            "database": "supply_chain",
            "username": "supply_admin",
            "password": "supply_pass654",
            "max_connections": 18,
            "connection_timeout": 60,
            "refresh_time": "05:30",
            "status": "active"
        }
    ]
    
    db_path = get_db_path()
    print(f"📍 数据库路径: {db_path}")
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查当前数据源数量
        cursor.execute("SELECT COUNT(*) FROM data_sources")
        current_count = cursor.fetchone()[0]
        print(f"📊 当前数据源数量: {current_count}")
        
        # 准备插入SQL
        insert_sql = """
        INSERT INTO data_sources 
        (name, description, db_type, host, port, database, username, password, 
         max_connections, connection_timeout, refresh_time, status, created_at, updated_at, created_by)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # 插入每个数据源
        for i, ds in enumerate(new_datasources, 1):
            try:
                # 加密密码
                encrypted_password = encrypt_password(ds["password"])
                
                # 准备插入数据
                insert_data = (
                    ds["name"],
                    ds["description"],
                    ds["db_type"],
                    ds["host"],
                    ds["port"],
                    ds["database"],
                    ds["username"],
                    encrypted_password,
                    ds["max_connections"],
                    ds["connection_timeout"],
                    ds["refresh_time"],
                    ds["status"],
                    current_time,
                    current_time,
                    "admin"
                )
                
                cursor.execute(insert_sql, insert_data)
                print(f"✅ 已添加数据源 {i}: {ds['name']}")
                
            except sqlite3.IntegrityError as e:
                if "UNIQUE constraint failed" in str(e):
                    print(f"⚠️  数据源 {ds['name']} 已存在，跳过")
                else:
                    print(f"❌ 添加数据源 {ds['name']} 失败: {e}")
            except Exception as e:
                print(f"❌ 添加数据源 {ds['name']} 失败: {e}")
        
        # 提交事务
        conn.commit()
        
        # 检查最终数据源数量
        cursor.execute("SELECT COUNT(*) FROM data_sources")
        final_count = cursor.fetchone()[0]
        print(f"📊 最终数据源数量: {final_count}")
        print(f"🎉 成功添加 {final_count - current_count} 个数据源!")
        
        # 显示所有数据源
        cursor.execute("SELECT id, name, db_type, host FROM data_sources ORDER BY id")
        all_datasources = cursor.fetchall()
        print("\n📋 所有数据源列表:")
        for ds in all_datasources:
            print(f"  {ds[0]}: {ds[1]} ({ds[2]}) - {ds[3]}")
        
    except Exception as e:
        print(f"❌ 操作失败: {e}")
        if 'conn' in locals():
            conn.rollback()
    
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    add_datasources()
