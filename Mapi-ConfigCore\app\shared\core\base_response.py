"""
基础响应处理器
提供通用的日志记录和响应处理功能
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from enum import Enum

from app.shared.core.log_util import LogUtil


class ErrorType(Enum):
    """错误分类枚举"""
    验证错误 = "VALIDATION_ERROR"
    数据库错误 = "DATABASE_ERROR"
    未知错误 = "UNKNOWN_ERROR"
    资源未找到 = "RESOURCE_NOT_FOUND"
    资源冲突 = "RESOURCE_CONFLICT"
    业务规则违反 = "BUSINESS_RULE_VIOLATION"
    无效操作 = "INVALID_OPERATION"
    认证失败 = "AUTHENTICATION_FAILED"
    授权不足 = "AUTHORIZATION_INSUFFICIENT"


class BaseResponseHandler(ABC):
    """基础响应处理器抽象基类"""
    
    @staticmethod
    def log_business_info(message: str, detail: Dict[str, Any] = None):
        """记录业务信息日志"""
        LogUtil.biz_info(message, **(detail or {}))

    @staticmethod
    def log_business_error(error_type: ErrorType, message: str, detail: Dict[str, Any] = None):
        """记录业务错误日志"""
        LogUtil.biz_info(f"[BIZ-{error_type.name}] {message}", **(detail or {}))
    
    @staticmethod
    def log_technical_error(message: str, detail: Dict[str, Any] = None):
        """记录技术错误日志"""
        # 避免detail中的message键与参数message冲突
        detail_copy = (detail or {}).copy()
        detail_copy.pop('message', None)  # 移除可能冲突的message键
        LogUtil.tech_error(message, **detail_copy)
    
    @staticmethod
    def create_error_response(
        success: bool = False,
        message: str = "",
        error_code: str = "",
        detail: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """创建标准错误响应格式"""
        return {
            "success": success,
            "message": message,
            "error_code": error_code,
            "detail": detail or {}
        }
    
    @staticmethod
    def create_success_response(
        message: str = "操作成功",
        data: Any = None
    ) -> Dict[str, Any]:
        """创建标准成功响应格式"""
        response = {
            "success": True,
            "message": message
        }
        if data is not None:
            response["data"] = data
        return response
    
    @staticmethod
    def get_http_status_code(error_type: ErrorType) -> int:
        """根据错误类型获取HTTP状态码"""
        from fastapi import status
        
        mapping = {
            ErrorType.验证错误: status.HTTP_400_BAD_REQUEST,
            ErrorType.资源未找到: status.HTTP_404_NOT_FOUND,
            ErrorType.资源冲突: status.HTTP_409_CONFLICT,
            ErrorType.业务规则违反: status.HTTP_422_UNPROCESSABLE_ENTITY,
            ErrorType.无效操作: status.HTTP_400_BAD_REQUEST,
            ErrorType.认证失败: status.HTTP_401_UNAUTHORIZED,
            ErrorType.授权不足: status.HTTP_403_FORBIDDEN,
            ErrorType.数据库错误: status.HTTP_500_INTERNAL_SERVER_ERROR,
            ErrorType.未知错误: status.HTTP_500_INTERNAL_SERVER_ERROR,
        }
        return mapping.get(error_type, status.HTTP_400_BAD_REQUEST)
