"""
异常处理器
处理系统异常和技术异常
"""

import traceback
import inspect
from datetime import datetime
from typing import Dict, Any, Optional
from fastapi import HTTPException, status
from fastapi.responses import JSONResponse

from .base_response import BaseResponse<PERSON><PERSON><PERSON>, ErrorType


class BusinessException(HTTPException):
    """业务预期异常（用户可见详情）"""
    
    def __init__(
        self,
        user_message: str,
        user_detail: Dict[str, Any],
        error_type: ErrorType,
        status_code: Optional[int] = None
    ):
        # 设置HTTP状态码
        if status_code is None:
            status_code = BaseResponseHandler.get_http_status_code(error_type)
        
        # 记录业务日志（INFO级别）
        BaseResponseHandler.log_business_error(
            error_type,
            user_message,
            user_detail
        )
        
        super().__init__(
            status_code=status_code,
            detail={
                "message": user_message,
                "detail": user_detail
            }
        )


class TechnicalException(HTTPException):
    """技术意外异常（开发者专用）"""
    
    def __init__(
        self,
        error_type: ErrorType,
        developer_detail: Dict[str, Any],
        status_code: Optional[int] = None
    ):
        # 设置HTTP状态码
        if status_code is None:
            status_code = BaseResponseHandler.get_http_status_code(error_type)
        
        # 自动捕获调试信息
        frame = inspect.currentframe().f_back
        debug_info = {
            "timestamp": datetime.utcnow().isoformat(),
            "file": frame.f_code.co_filename,
            "line": frame.f_lineno,
            "function": frame.f_code.co_name,
            "stack_trace": traceback.format_exc(),
        }
        
        # 合并开发者详情和调试信息
        full_detail = {**developer_detail, **debug_info}
        
        # 记录技术日志（ERROR级别）
        BaseResponseHandler.log_technical_error(
            f"[{error_type.name}] 技术异常",
            full_detail
        )
        
        super().__init__(
            status_code=status_code,
            detail={
                "message": "发生意外错误",
                "detail": self._filter_user_safe_detail(developer_detail)
            }
        )
    
    @staticmethod
    def _filter_user_safe_detail(detail: Dict[str, Any]) -> Dict[str, Any]:
        """过滤用户可见的详情信息"""
        # 只保留业务安全的字段
        safe_keys = {"operation", "resource_id"}
        return {k: v for k, v in detail.items() if k in safe_keys}


class ExceptionHandler(BaseResponseHandler):
    """异常处理器"""
    
    @classmethod
    def register_handlers(cls, app):
        """注册全局异常处理器"""
        
        @app.exception_handler(HTTPException)
        async def handle_http_exception(request, exc: HTTPException):
            # 检查是否是BusinessException
            if isinstance(exc, BusinessException):
                cls.log_business_info("BusinessException处理器被调用", {"message": str(exc.detail), "status_code": exc.status_code})
                return JSONResponse(
                    status_code=exc.status_code,
                    content=exc.detail
                )
            else:
                return JSONResponse(
                    status_code=exc.status_code,
                    content={"message": exc.detail}
                )
        
        @app.exception_handler(BusinessException)
        async def handle_business_exception(request, exc: BusinessException):
            cls.log_business_info("BusinessException处理器被调用", {"status_code": exc.status_code})
            return JSONResponse(
                status_code=exc.status_code,
                content=exc.detail
            )
        
        @app.exception_handler(TechnicalException)
        async def handle_technical_exception(request, exc: TechnicalException):
            return JSONResponse(
                status_code=exc.status_code,
                content=exc.detail
            )
        
        @app.exception_handler(Exception)
        async def handle_generic_exception(request, exc: Exception):
            # 自动捕获调试信息
            debug_info = {
                "timestamp": datetime.utcnow().isoformat(),
                "stack_trace": traceback.format_exc(),
                "request_path": request.url.path,
                "request_method": request.method,
            }
            
            # 记录未处理异常（ERROR级别）
            cls.log_technical_error(
                f"UNHANDLED_EXCEPTION: {str(exc)}",
                {
                    "type": "UNHANDLED",
                    "exception_type": type(exc).__name__,
                    "context": debug_info
                }
            )
            
            # 用户安全响应
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={
                    "message": "发生意外错误",
                    "detail": {}
                }
            )
