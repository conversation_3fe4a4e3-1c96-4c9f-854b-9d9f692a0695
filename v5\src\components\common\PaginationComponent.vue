<!-- 
  PaginationComponent - 通用分页组件
  功能: 提供标准的分页控制，支持页码切换、每页条数调整，并通过事件与父组件通信
  Props:
    currentPage - 当前页码
    pageSize - 每页条数
    total - 总记录数
    pageSizes - 可选的每页条数列表
    layout - 分页布局配置
    background - 是否显示背景
    small - 是否使用小型分页样式
  Events:
    update:currentPage - 页码变更事件
    update:pageSize - 每页条数变更事件
    sizeChange - 每页条数改变时触发
    currentChange - 页码改变时触发
-->
<template>
  <div class="pagination-container">
    <el-pagination
      v-model:current-page="currentPageModel"
      v-model:page-size="pageSizeModel"
      :page-sizes="pageSizes"
      :total="total"
      :layout="layout"
      :background="background"
      :small="small"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  currentPage: number;
  pageSize: number;
  total: number;
  pageSizes?: number[];
  layout?: string;
  background?: boolean;
  small?: boolean;
}

interface Emits {
  (e: 'update:currentPage', value: number): void;
  (e: 'update:pageSize', value: number): void;
  (e: 'sizeChange', size: number): void;
  (e: 'currentChange', page: number): void;
}

const props = withDefaults(defineProps<Props>(), {
  pageSizes: () => [10, 15, 20, 50, 100], // 添加15选项，支持3行5列布局
  layout: 'total, sizes, prev, pager, next, jumper',
  background: true,
  small: false
});

const emit = defineEmits<Emits>();

// 双向绑定
const currentPageModel = computed({
  get: () => props.currentPage,
  set: (value: number) => emit('update:currentPage', value)
});

const pageSizeModel = computed({
  get: () => props.pageSize,
  set: (value: number) => emit('update:pageSize', value)
});

// 事件处理
const handleSizeChange = (size: number) => {
  emit('sizeChange', size);
};

const handleCurrentChange = (page: number) => {
  emit('currentChange', page);
};
</script>

<style scoped>
.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

/* 让分页数字背景色与搜索框背景色保持一致 */
:deep(.el-pagination) {
  .el-pager li {
    background-color: #ffffff; /* 与搜索框背景色一致 */
    border: 1px solid #dcdfe6;

    &:hover {
      background-color: #f5f7fa;
    }

    &.is-active {
      background-color: #3FC8DD; /* 与搜索框主题色一致 */
      color: #ffffff; /* 白色文字 */
      border-color: #3FC8DD; /* 边框也使用相同颜色 */
    }
  }

  .btn-prev,
  .btn-next {
    background-color: #ffffff; /* 与搜索框背景色一致 */
    border: 1px solid #dcdfe6;

    &:hover {
      background-color: #f5f7fa;
    }
  }

  .el-pagination__jump input {
    background-color: #ffffff; /* 跳转输入框也保持一致 */
  }
}
</style>
