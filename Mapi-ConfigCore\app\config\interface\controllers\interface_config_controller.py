"""
接口配置控制器层
处理HTTP请求和响应
"""

from fastapi import Depends
from sqlalchemy.orm import Session
from typing import Optional
from app.config.interface.services.interface_config_service import InterfaceConfigService
from app.config.interface.schemas.interface_config_schema import (
    InterfaceConfigCreate,
    InterfaceConfigUpdate,
    InterfaceConfigResponse,
    InterfaceConfigListResponse
)
from app.shared.database import get_database
from app.shared.core.log_util import LogUtil


class InterfaceConfigController:
    """接口配置控制器类"""
    
    def __init__(self, db: Session = Depends(get_database)):
        self.db = db
        self.service = InterfaceConfigService(db)
        LogUtil.debug("接口配置控制器初始化", controller="InterfaceConfigController")
    
    async def get_interface_configs(
        self,
        page: int = 1,
        size: int = 10,
        search: Optional[str] = None,
        group_id: Optional[int] = None,
        method: Optional[str] = None,
        is_enabled: Optional[bool] = None
    ) -> InterfaceConfigListResponse:
        """
        获取接口配置列表
        
        Args:
            page: 页码，默认1
            size: 每页大小，默认10
            search: 搜索关键词，可选
            group_id: 分组ID过滤，可选
            method: HTTP方法过滤，可选
            is_enabled: 启用状态过滤，可选
            
        Returns:
            接口配置列表响应
        """
        LogUtil.debug("处理获取接口配置列表请求", 
                     controller="InterfaceConfigController",
                     action="get_interface_configs",
                     page=page, size=size, search=search,
                     group_id=group_id, method=method, is_enabled=is_enabled)
        
        return self.service.get_interface_configs(page, size, search, group_id, method, is_enabled)
    
    async def get_interface_config(self, config_id: int) -> InterfaceConfigResponse:
        """
        获取单个接口配置
        
        Args:
            config_id: 接口配置ID
            
        Returns:
            接口配置响应
        """
        LogUtil.debug("处理获取接口配置详情请求", 
                     controller="InterfaceConfigController",
                     action="get_interface_config",
                     config_id=config_id)
        
        return self.service.get_interface_config(config_id)
    
    async def create_interface_config(self, config_data: InterfaceConfigCreate) -> InterfaceConfigResponse:
        """
        创建接口配置
        
        Args:
            config_data: 接口配置创建数据
            
        Returns:
            创建的接口配置响应
        """
        LogUtil.debug("处理创建接口配置请求",
                     controller="InterfaceConfigController",
                     action="create_interface_config",
                     name=config_data.name,
                     path=config_data.path,
                     method=config_data.method)

        # 调试日志：检查table_type字段
        print(f"=== 控制器接收到的数据调试 ===")
        print(f"config_data.table_type: {config_data.table_type}")
        print(f"完整接收数据: {config_data.dict()}")
        
        return self.service.create_interface_config(config_data)
    
    async def update_interface_config(
        self, 
        config_id: int, 
        config_data: InterfaceConfigUpdate
    ) -> InterfaceConfigResponse:
        """
        更新接口配置
        
        Args:
            config_id: 接口配置ID
            config_data: 更新数据
            
        Returns:
            更新后的接口配置响应
        """
        LogUtil.debug("处理更新接口配置请求", 
                     controller="InterfaceConfigController",
                     action="update_interface_config",
                     config_id=config_id)
        
        return self.service.update_interface_config(config_id, config_data)
    
    async def delete_interface_config(self, config_id: int) -> dict:
        """
        删除接口配置
        
        Args:
            config_id: 接口配置ID
            
        Returns:
            删除结果
        """
        LogUtil.debug("处理删除接口配置请求", 
                     controller="InterfaceConfigController",
                     action="delete_interface_config",
                     config_id=config_id)
        
        return self.service.delete_interface_config(config_id)
