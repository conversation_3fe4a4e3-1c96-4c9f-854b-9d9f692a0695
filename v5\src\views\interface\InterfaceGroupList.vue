# Copyright (c) 2025 左岚. All rights reserved.
<template>
  <div class="container">
    <div class="page-header">
      <div class="page-title">
        <el-icon><Collection /></el-icon>
        <span class="title-text">接口分组设置</span>
      </div>
      <div class="header-actions">
        <!-- 视图切换按钮移到左侧 -->
        <div class="view-toggle">
          <el-button-group>
            <el-button
              :type="viewMode === 'card' ? 'primary' : ''"
              size="default"
              @click="viewMode = 'card'"
              title="卡片视图"
            >
              <el-icon><Grid /></el-icon>
            </el-button>
            <el-button
              :type="viewMode === 'list' ? 'primary' : ''"
              size="default"
              @click="viewMode = 'list'"
              title="列表视图"
            >
              <el-icon><List /></el-icon>
            </el-button>
          </el-button-group>
        </div>

        <SearchComponent
          v-model="searchQuery"
          placeholder="搜索接口分组"
          width="300px"
          @search="handleSearch"
          @clear="handleSearch"
        />

        <el-button type="primary" @click="handleAdd">新增分组</el-button>
        <el-button
          :icon="Refresh"
          @click="loadInterfaceGroups"
          class="refresh-btn"
        >
        刷新
        </el-button>

      </div>
    </div>

    <!-- 直接显示内容，移除页签 -->
    <div class="content-area">
      <!-- 卡片视图 -->
        <div v-if="viewMode === 'card'" class="card-view">
          <div v-loading="loading" class="card-grid">
            <BaseCard
              v-for="(interfaceGroup, index) in filteredInterfaceGroups"
              :key="interfaceGroup.id"
              :item="interfaceGroup"
              :config="{
                titleField: 'name',
                statusField: 'isEnabled',
                size: 'small',
                shadow: 'hover',
                style: 'compact-info'
              }"
              :class="[`status-${interfaceGroup.isEnabled ? 'enabled' : 'disabled'}`]"
              :show-default-header="false"
              :show-default-actions="false"
              @click="handleView(interfaceGroup)"
              @edit="(item, event) => handleEdit(item, event)"
              @delete="(item, event) => handleDelete(item, event)"
              style="cursor: pointer"
            >
              <template #content="{ item }">
                <!-- 接口分组卡片内容 -->
                <div class="interfacegroup-card-content">
                  <!-- 卡片头部：标题 + 状态 -->
                  <div class="card-header">
                    <h4 class="interfacegroup-title" :title="item.name">{{ item.name }}</h4>
                    <el-tag :type="getStatusType(item.isEnabled)" size="small" class="status-tag">
                      <el-icon class="status-icon">
                        <CircleCheck v-if="item.isEnabled" />
                        <CircleClose v-else />
                      </el-icon>
                      {{ getStatusText(item.isEnabled) }}
                    </el-tag>
                  </div>

                  <!-- 分组信息：图标+文字左对齐显示 -->
                  <div class="group-info">
                    <div class="info-row">
                      <div class="info-item">
                        <el-icon class="info-icon"><Link /></el-icon>
                        <span class="info-text">/api/v1/{{ item.pathPrefix }}</span>
                      </div>
                      <div class="info-item">
                        <el-icon class="info-icon"><Document /></el-icon>
                        <span class="info-text">{{ item.interfaceCount || 0 }} 个接口</span>
                      </div>
                    </div>
                  </div>

                  <!-- 底部栏：分组类型 + 创建时间 -->
                  <div class="status-bar">
                    <el-tag type="info" size="small" class="group-type-tag">
                      接口分组
                    </el-tag>
                    <div class="create-time">
                      <el-icon class="time-icon"><Clock /></el-icon>
                      <span class="time-text">{{ formatTime(item.createdAt) }}</span>
                    </div>
                  </div>

                </div>
              </template>

              <template #actions="{ item }">
                <div class="card-actions">
                  <el-button type="primary" size="small" circle @click="handleEdit(item, $event)" title="编辑">
                    <el-icon><Edit /></el-icon>
                  </el-button>
                  <el-button size="small" type="danger" circle @click="handleDelete(item, $event)" title="删除">
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>
              </template>
            </BaseCard>
          </div>

          <!-- 卡片视图空状态 -->
          <div v-if="!loading && filteredInterfaceGroups.length === 0" class="card-empty">
            <EmptyState
              :type="searchQuery ? 'search' : 'table'"
              :title="searchQuery ? '无搜索结果' : '暂无接口分组'"
              :description="searchQuery ? `没有找到包含「${searchQuery}」的接口分组，请尝试其他搜索条件` : '当前没有配置任何接口分组，您可以点击上方按钮添加新的接口分组'"
              :action-text="searchQuery ? '清除搜索' : '添加分组'"
              @action="searchQuery ? clearSearch : handleAdd"
            />
          </div>
        </div>

        <!-- 列表视图 -->
        <div v-else-if="viewMode === 'list'" class="list-view">
          <!-- 接口分组列表 -->
        <el-table
          v-loading="loading"
          :data="filteredInterfaceGroups"
          style="width: 100%; min-width: 1000px;"
          :row-style="{ height: '60px' }"
          :cell-style="{ padding: '12px 0' }"
          :scroll-x="true"
        >
      <el-table-column label="分组名称" prop="name" min-width="120">
        <template #default="{ row }">
          <div class="interfacegroup-name">
            <el-icon><Collection /></el-icon>
            {{ row.name }}
          </div>
        </template>
      </el-table-column>

      <el-table-column label="路径前缀" prop="pathPrefix" min-width="120">
        <template #default="{ row }">
          <code class="path-prefix">/api/v1/{{ row.pathPrefix }}</code>
        </template>
      </el-table-column>

      <el-table-column label="接口数量" prop="interfaceCount" min-width="80">
        <template #default="{ row }">
          <span class="interface-count">{{ row.interfaceCount || 0 }} 个</span>
        </template>
      </el-table-column>
      
      <el-table-column label="状态" min-width="100">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.isEnabled)" effect="dark">
            <el-icon v-if="row.isEnabled"><CircleCheck /></el-icon>
            <el-icon v-else><CircleClose /></el-icon>
            {{ getStatusText(row.isEnabled) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="创建时间" prop="createdAt" min-width="120">
        <template #default="{ row }">
          {{ formatTime(row.createdAt) }}
        </template>
      </el-table-column>

      <el-table-column label="操作" width="120" fixed="right">
        <template #default="{ row }">
          <div class="table-actions">
            <el-button type="primary" size="small" @click="handleEdit(row)" title="编辑">
              <el-icon><Edit /></el-icon>
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)" title="删除">
              <el-icon><Delete /></el-icon>
            </el-button>
          </div>
        </template>
      </el-table-column>

      <!-- 空状态插槽 -->
      <template #empty>
        <EmptyState
          :type="searchQuery ? 'search' : 'table'"
          :title="searchQuery ? '无搜索结果' : '暂无接口分组'"
          :description="searchQuery ? `没有找到包含「${searchQuery}」的接口分组，请尝试其他搜索条件` : '当前没有配置任何接口分组，您可以点击上方按钮添加新的接口分组'"
          :action-text="searchQuery ? '清除搜索' : '添加分组'"
          @action="searchQuery ? clearSearch : handleAdd"
        />
      </template>
    </el-table>
        </div>

        <!-- 分页组件 -->
        <PaginationComponent
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  Collection, Grid, List, Refresh, Edit, Delete, CircleCheck, CircleClose,
  Link, Document, Clock
} from '@element-plus/icons-vue';
import BaseCard from '@/components/common/BaseCard.vue';
import SearchComponent from '@/components/common/SearchComponent.vue';
import PaginationComponent from '@/components/common/PaginationComponent.vue';
import EmptyState from '@/components/common/EmptyState.vue';
import interfaceGroupService from '@/services/interface-group.service';
import type { InterfaceGroup } from '@/types/interface-group';
import { useGlobalDrawerMessenger } from '@/utils/piniaMessenger';
import { DateTimeUtils } from '@/utils/common-utils';

// 全局抽屉通信
const drawerMessenger = useGlobalDrawerMessenger();

// 响应式数据
const loading = ref(false);
const viewMode = ref<'card' | 'list'>('card'); // 默认卡片视图
const searchQuery = ref('');
const currentPage = ref(1);
const pageSize = ref(15); // 每页15个卡片
const totalCount = ref(0);
const interfaceGroups = ref<InterfaceGroup[]>([]);

// 计算属性 - 过滤后的接口分组
const filteredInterfaceGroups = computed(() => {
  if (!searchQuery.value) {
    return interfaceGroups.value;
  }
  const query = searchQuery.value.toLowerCase();
  return interfaceGroups.value.filter(group =>
    group.name.toLowerCase().includes(query) ||
    group.pathPrefix.toLowerCase().includes(query) ||
    (group.description && group.description.toLowerCase().includes(query))
  );
});

// 状态相关方法
const getStatusType = (isEnabled: boolean) => {
  return isEnabled ? 'success' : 'danger';
};

const getStatusText = (isEnabled: boolean) => {
  return isEnabled ? '启用' : '禁用';
};

// 时间格式化
const formatTime = (time: string) => {
  return DateTimeUtils.formatDate(time);
};

// 加载接口分组列表
const loadInterfaceGroups = async () => {
  loading.value = true;
  try {
    const response = await interfaceGroupService.getInterfaceGroups({
      page: currentPage.value,
      pageSize: pageSize.value,
      search: searchQuery.value || undefined
    });

    interfaceGroups.value = response.items;
    totalCount.value = response.total;
  } catch (error) {
    ElMessage.error('加载接口分组列表失败');
    console.error('Load interface groups error:', error);
  } finally {
    loading.value = false;
  }
};

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1;
  loadInterfaceGroups();
};

// 清除搜索
const clearSearch = () => {
  searchQuery.value = '';
  handleSearch();
};

// 分页处理
const handleSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
  loadInterfaceGroups();
};

const handleCurrentChange = (page: number) => {
  currentPage.value = page;
  loadInterfaceGroups();
};

// 查看接口分组详情
const handleView = async (row: InterfaceGroup) => {
  try {
    // 从后端获取完整的接口分组信息
    const fullInterfaceGroup = await interfaceGroupService.getInterfaceGroupById(row.id);
    if (!fullInterfaceGroup) {
      ElMessage.error('接口分组不存在或已被删除');
      return;
    }

    drawerMessenger.showDrawer({
      title: '查看接口分组',
      component: 'InterfaceGroupForm',
      props: {
        isEdit: false, // 查看模式，不显示保存按钮
        isView: true,  // 标识为查看模式
        editData: fullInterfaceGroup // 使用从后端获取的完整数据
      },
      size: '28%'
    });
  } catch (error) {
    console.error('获取接口分组详情失败:', error);
    ElMessage.error('获取接口分组详情失败，请稍后重试');
  }
};

// 编辑接口分组
const handleEdit = async (row: InterfaceGroup, event?: Event) => {
  if (event) {
    event.stopPropagation(); // 阻止事件冒泡
  }

  try {
    // 从后端获取完整的接口分组信息
    const fullInterfaceGroup = await interfaceGroupService.getInterfaceGroupById(row.id);
    if (!fullInterfaceGroup) {
      ElMessage.error('接口分组不存在或已被删除');
      return;
    }

    drawerMessenger.showDrawer({
      title: '编辑接口分组',
      component: 'InterfaceGroupForm',
      props: {
        isEdit: true,
        editData: fullInterfaceGroup // 使用从后端获取的完整数据
      },
      size: '28%'
    });
  } catch (error) {
    console.error('获取接口分组详情失败:', error);
    ElMessage.error('获取接口分组详情失败，请稍后重试');
  }
};

// 新增接口分组
const handleAdd = () => {
  drawerMessenger.showDrawer({
    title: '新增接口分组',
    component: 'InterfaceGroupForm',
    props: {
      isEdit: false
    },
    size: '28%'
  });
};

// 删除接口分组
const handleDelete = async (row: InterfaceGroup, event?: Event) => {
  if (event) {
    event.stopPropagation(); // 阻止事件冒泡
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除接口分组「${row.name}」吗？删除后不可恢复。`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    );

    await interfaceGroupService.deleteInterfaceGroup(row.id);
    ElMessage.success('接口分组删除成功');

    // 删除后刷新列表
    await loadInterfaceGroups();
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除接口分组失败:', error);
      ElMessage.error('删除接口分组失败，请稍后重试');
    }
  }
};

// 页面加载时获取数据
onMounted(() => {
  loadInterfaceGroups();
});
</script>

<style lang="scss" scoped>
@use '@/assets/styles/page-common.scss' as *;

/* 卡片网格布局样式 - 参考数据源设置 */
.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr)); // 减小最小宽度，便于每行显示更多卡片
  gap: 16px;
  margin-bottom: 24px;
  padding: 16px 0;
}

/* 接口分组管理页面特有样式 */
.interfacegroup-card-content {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    .interfacegroup-title {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
      color: #303133;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      text-align: left; // 确保左对齐
      margin-right: 8px; // 与状态标签保持间距
    }

    .status-tag {
      margin-left: 8px;
      flex-shrink: 0;

      .status-icon {
        margin-right: 4px;
        font-size: 12px;
      }
    }
  }

  .group-info {
    margin-bottom: 12px;

    .info-row {
      display: flex;
      flex-direction: column;
      gap: 6px;
    }

    .info-item {
      display: flex;
      align-items: center;
      font-size: 13px;
      color: #606266;

      .info-icon {
        margin-right: 6px;
        font-size: 14px;
        color: #909399;
        flex-shrink: 0;
      }

      .info-text {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;

    .group-type-tag {
      flex-shrink: 0;
    }

    .create-time {
      display: flex;
      align-items: center;
      font-size: 12px;
      color: #909399;

      .time-icon {
        margin-right: 4px;
        font-size: 12px;
      }

      .time-text {
        white-space: nowrap;
      }
    }
  }
}

/* 列表视图样式 */
.interfacegroup-name {
  display: flex;
  align-items: center;
  font-weight: 500;

  .el-icon {
    margin-right: 8px;
    font-size: 16px;
    color: #3FC8DD;
  }
}

.path-prefix {
  background: #f5f7fa;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #606266;
}

.interface-count {
  font-weight: 500;
  color: #409eff;
}

.table-actions {
  display: flex;
  gap: 8px;
}

/* 卡片操作按钮样式 - 参考数据源设置 */
.card-actions {
  display: flex;
  justify-content: flex-end; // 按钮右对齐
  gap: 6px;
  margin-top: 11px; // 增加上边距，让按钮下移
  margin-bottom: 6px; // 距离底部6px
}
</style>
