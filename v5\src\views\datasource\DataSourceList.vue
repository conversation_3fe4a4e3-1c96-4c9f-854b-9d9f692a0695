<template>
  <div class="container">
    <div class="page-header">
      <div class="page-title">
        <el-icon><DataLine /></el-icon>
        <span class="title-text">数据源设置</span>
      </div>
      <div class="header-actions">
        <!-- 视图切换按钮移到左侧 -->
        <div class="view-toggle">
          <el-button-group>
            <el-button
              :type="viewMode === 'card' ? 'primary' : ''"
              size="default"
              @click="viewMode = 'card'"
              title="卡片视图"
            >
              <el-icon><Grid /></el-icon>
            </el-button>
            <el-button
              :type="viewMode === 'list' ? 'primary' : ''"
              size="default"
              @click="viewMode = 'list'"
              title="列表视图"
            >
              <el-icon><List /></el-icon>
            </el-button>
          </el-button-group>
        </div>

        <SearchComponent
          v-model="searchQuery"
          placeholder="搜索数据源"
          width="300px"
          @search="handleSearch"
          @clear="handleSearch"
        />

        <el-button type="primary" @click="handleAdd">新增数据源</el-button>
        <el-button
          :icon="Refresh"
          @click="loadDataSources"
          class="refresh-btn"
        >
        刷新
        </el-button>

      </div>
    </div>

    <!-- 直接显示内容，移除页签 -->
    <div class="content-area">
      <!-- 卡片视图 -->
        <div v-if="viewMode === 'card'" class="card-view">
          <div v-loading="loading" class="card-grid">
            <BaseCard
              v-for="(dataSource, index) in filteredDataSources"
              :key="dataSource.id"
              :item="dataSource"
              :config="{
                titleField: 'name',
                statusField: 'status',
                size: 'small',
                shadow: 'hover',
                style: getCardStyle(index)
              }"
              :class="[getDbTypeClass(dataSource), `status-${dataSource.status}`]"
              :show-default-header="false"
              :show-default-actions="false"
              @click="handleView(dataSource)"
              @edit="(item, event) => handleEdit(item, event)"
              @test="(item, event) => handleTest(item, event)"
              @delete="(item, event) => handleDelete(item, event)"
              style="cursor: pointer"
            >
              <template #content="{ item }">
                <!-- 数据源卡片内容 -->
                <div class="datasource-card-content">
                  <!-- 卡片头部：标题 + 状态 -->
                  <div class="card-header">
                    <h4 class="datasource-title">{{ item.name }}</h4>
                    <el-tag :type="getStatusType(item.status)" size="small" class="status-tag">
                      <el-icon class="status-icon">
                        <CircleCheck v-if="item.status === 'active'" />
                        <CircleClose v-else-if="item.status === 'inactive'" />
                        <InfoFilled v-else />
                      </el-icon>
                      {{ getStatusText(item.status) }}
                    </el-tag>
                  </div>

                  <!-- 连接信息：图标+文字左对齐显示 -->
                  <div class="connection-info">
                    <div class="info-row">
                      <div class="info-item">
                        <el-icon class="info-icon"><Connection /></el-icon>
                        <span class="info-text">{{ item.host }}:{{ item.port }}</span>
                      </div>
                      <div class="info-item">
                        <el-icon class="info-icon"><Folder /></el-icon>
                        <span class="info-text">{{ item.database }}</span>
                      </div>
                    </div>
                  </div>

                  <!-- 底部栏：数据库类型 + 更新时间 -->
                  <div class="status-bar">
                    <el-tag :type="getDbTypeTag(item.dbType)" size="small" class="db-type-tag">
                      {{ getDbTypeName(item.dbType) }}
                    </el-tag>
                    <div class="update-time">
                      <el-icon class="time-icon"><Clock /></el-icon>
                      <span class="time-text">{{ formatTime(item.updatedAt) }}</span>
                    </div>
                  </div>


                </div>
              </template>

              <template #actions="{ item }">
                <div class="card-actions">
                  <el-button type="primary" size="small" circle @click="handleEdit(item, $event)" title="编辑">
                    <el-icon><Edit /></el-icon>
                  </el-button>
                  <el-button size="small" type="info" circle @click="handleTest(item, $event)" title="测试连接">
                    <el-icon><Connection /></el-icon>
                  </el-button>
                  <el-button size="small" type="danger" circle @click="handleDelete(item, $event)" title="删除">
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>
              </template>
            </BaseCard>
          </div>

          <!-- 卡片视图空状态 -->
          <div v-if="!loading && filteredDataSources.length === 0" class="card-empty">
            <EmptyState
              :type="searchQuery ? 'search' : 'table'"
              :title="searchQuery ? '无搜索结果' : '暂无数据源'"
              :description="searchQuery ? `没有找到包含「${searchQuery}」的数据源，请尝试其他搜索条件` : '当前没有配置任何数据源，您可以点击上方按钮添加新的数据源'"
              :action-text="searchQuery ? '清除搜索' : '添加数据源'"
              @action="searchQuery ? clearSearch : handleAdd"
            />
          </div>
        </div>

        <!-- 列表视图 -->
        <div v-else-if="viewMode === 'list'" class="list-view">
          <!-- 数据源列表 -->
        <el-table
          v-loading="loading"
          :data="filteredDataSources"
          style="width: 100%; min-width: 1000px;"
          :row-style="{ height: '60px' }"
          :cell-style="{ padding: '12px 0' }"
          :scroll-x="true"
        >
      <el-table-column label="数据源名称" prop="name" min-width="100">
        <template #default="{ row }">
          <div class="datasource-name">
            <el-icon v-if="row.dbType === 'mysql'"><Cpu /></el-icon>
            <el-icon v-else-if="row.dbType === 'postgres'"><Cpu /></el-icon>
            <el-icon v-else-if="row.dbType === 'sqlserver'"><Cpu /></el-icon>
            <el-icon v-else-if="row.dbType === 'oracle'"><Cpu /></el-icon>
            <el-icon v-else><Folder /></el-icon>
            {{ row.name }}
          </div>
        </template>
      </el-table-column>

      <el-table-column label="描述信息" prop="description" min-width="120" show-overflow-tooltip>
        <template #default="{ row }">
          <span class="description-text">{{ row.description || '暂无描述' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="数据库类型" prop="dbType" min-width="80">
        <template #default="{ row }">
          <el-tag :type="getDbTypeTag(row.dbType)">
            {{ getDbTypeName(row.dbType) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="连接信息" min-width="150">
        <template #default="{ row }">
          {{ row.host }}{{ row.port ? `:${row.port}` : '' }}
          <div class="db-name">{{ row.database }}</div>
        </template>
      </el-table-column>
      
      <el-table-column label="状态" min-width="100">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)" effect="dark">
            <el-icon v-if="row.status === 'active'"><CircleCheck /></el-icon>
            <el-icon v-else-if="row.status === 'inactive'"><InfoFilled /></el-icon>
            <el-icon v-else><CircleClose /></el-icon>
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="更新时间" min-width="160">
        <template #default="{ row }">
          {{ DateTimeUtils.formatStandardDateTime(row.updatedAt) }}
        </template>
      </el-table-column>
      
      <el-table-column label="操作" min-width="200" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" size="small" @click="handleEdit(row)">
            编辑
          </el-button>
          <el-button size="small" type="info" @click="handleTest(row)">
            测试
          </el-button>
          <el-button size="small" type="danger" @click="handleDelete(row)">
            删除
          </el-button>
        </template>
      </el-table-column>

        <!-- 空状态插槽 -->
        <template #empty>
          <EmptyState
            :type="searchQuery ? 'search' : 'table'"
            :title="searchQuery ? '无搜索结果' : '暂无数据源'"
            :description="searchQuery ? `没有找到包含「${searchQuery}」的数据源，请尝试其他搜索条件` : '当前没有配置任何数据源，您可以点击上方按钮添加新的数据源'"
            :action-text="searchQuery ? '清除搜索' : '添加数据源'"
            @action="searchQuery ? clearSearch : handleAdd"
          />
        </template>
        </el-table>
        </div>

        <!-- 分页组件（卡片和列表共用） -->
        <PaginationComponent
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
    </div>

    <!-- 测试连接结果对话框 -->
    <el-dialog
      v-model="testDialogVisible"
      title="连接测试结果"
      width="800px"
      :close-on-click-modal="false"
      center
      :append-to-body="true"
      :destroy-on-close="true"
    >
      <div v-if="testResult === null" class="test-loading">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>正在测试连接...</span>
      </div>

      <div v-else class="test-result">
        <div :class="['result-header', testResult.success ? 'success' : 'error']">
          <el-icon v-if="testResult.success"><CircleCheck /></el-icon>
          <el-icon v-else><CircleClose /></el-icon>
          <span class="result-title">{{ testResult.success ? '连接成功' : '连接失败' }}</span>
        </div>

        <div class="result-content">
          <div class="result-message">
            {{ testResult.message }}
          </div>

          <div v-if="testResult.success && testResult.responseTime" class="result-details">
            <div class="detail-item">
              <span class="label">响应时间:</span>
              <span class="value">{{ testResult.responseTime }}ms</span>
            </div>
          </div>

          <div v-if="!testResult.success && testResult.errorDetail" class="result-error">
            <div class="error-title">错误详情:</div>
            <div class="error-content">{{ testResult.errorDetail }}</div>
          </div>
        </div>
      </div>

      <template #footer>
        <el-button @click="testDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>



    <!-- 删除确认对话框 -->
    <ConfirmDialog
      v-model="deleteDialogVisible"
      title="确认删除"
      :content="deleteConfirmContent"
      warning="此操作将永久删除该数据源，且无法恢复！"
      confirm-text="确认删除"
      confirm-type="danger"
      @confirm="confirmDelete"
      @cancel="cancelDelete"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, onUnmounted } from 'vue';
import { ElMessage } from 'element-plus';
import { handleApiError, handleSuccessMessage } from '@/utils/error-handler';
import { DataLine, CircleCheck, CircleClose, InfoFilled, Cpu, Folder, Refresh, Loading, Grid, List, Connection, Clock, Edit, Delete } from '@element-plus/icons-vue';
import ConfirmDialog from '@/components/common/ConfirmDialog.vue';
import SearchComponent from '@/components/common/SearchComponent.vue';
import PaginationComponent from '@/components/common/PaginationComponent.vue';
import EmptyState from '@/components/common/EmptyState.vue';
import BaseCard from '@/components/common/BaseCard.vue';
import dataSourceService from '@/services/datasource.service';
import type { DataSource, ConnectionTestResult } from '@/types/datasource';
import { useGlobalDrawerMessenger } from '@/utils/piniaMessenger';
import { pageRefreshUtil, PAGE_KEYS } from '@/utils/pageRefreshUtil';
import { DateTimeUtils } from '@/utils/common-utils';

// 页签状态
// const activeTab = ref('management');

// 视图模式状态（默认显示卡片）
const viewMode = ref<'card' | 'list'>('card');

// 所有卡片都使用财务管理系统的样式（compact-info 紧凑信息样式）
const getCardStyle = (_index: number) => {
  // 统一使用 compact-info 样式，这是财务管理系统使用的样式
  return 'compact-info';
};

// 根据数据库类型获取额外的CSS类
const getDbTypeClass = (dataSource: any) => {
  return `db-${dataSource.dbType}`;
};

// 获取数据库类型标签颜色
const getDbTypeTag = (dbType: string) => {
  const typeMap: Record<string, string> = {
    mysql: 'primary',
    postgresql: 'success',
    sqlserver: 'warning',
    oracle: 'danger',
    sqlite: 'info'
  };
  return typeMap[dbType] || '';
};

// 获取数据库类型名称
const getDbTypeName = (dbType: string) => {
  const nameMap: Record<string, string> = {
    mysql: 'MySQL',
    postgresql: 'PostgreSQL',
    sqlserver: 'SQL Server',
    oracle: 'Oracle',
    sqlite: 'SQLite'
  };
  return nameMap[dbType] || dbType?.toUpperCase() || '未知';
};

// 获取状态类型
const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    active: 'success',
    inactive: 'warning',
    error: 'danger'
  };
  return statusMap[status] || 'info';
};

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    active: '正常',
    inactive: '未激活',
    error: '连接失败'
  };
  return textMap[status] || '未知';
};

// 格式化时间（包含年份）
const formatTime = (dateTime: string) => {
  try {
    const date = new Date(dateTime);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  } catch {
    return dateTime?.slice(0, 10) || '--';
  }
};

// 数据源列表相关状态（原 useDataSourceList 组合式函数内容）
const loading = ref(false);
const dataSources = ref<DataSource[]>([]);
const searchQuery = ref('');
const currentPage = ref(1);
const pageSize = ref(15); // 默认显示15个卡片
const total = ref(0);
const totalCount = ref(0);

// 过滤后的数据源列表
const filteredDataSources = computed(() => {
  let result = dataSources.value || []; // 确保result不为undefined

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(ds =>
      ds.name.toLowerCase().includes(query) ||
      ds.host.toLowerCase().includes(query) ||
      ds.database.toLowerCase().includes(query)
    );
  }

  totalCount.value = result.length;

  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return result.slice(start, end);
});

// 加载数据源列表
const loadDataSources = async () => {
  loading.value = true;
  try {
    const response = await dataSourceService.getDataSources(currentPage.value, pageSize.value);
    dataSources.value = response?.items || []; // 确保items不为undefined
    total.value = response?.total || 0; // 确保total不为undefined
  } catch (error) {
    handleApiError(error, '加载数据源列表失败');
    // 确保错误时也有默认值
    dataSources.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

// 刷新到第一页的方法
const loadDataSourcesToFirstPage = async () => {
  currentPage.value = 1;
  await loadDataSources();
};

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1;
};

// 清除搜索
const clearSearch = () => {
  searchQuery.value = '';
  currentPage.value = 1;
};

// 分页大小变化处理
const handleSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
  loadDataSources();
};

// 当前页变化处理
const handleCurrentChange = (page: number) => {
  currentPage.value = page;
  loadDataSources();
};

// 全局抽屉通信助手
const drawerMessenger = useGlobalDrawerMessenger();



// 测试连接相关
const testDialogVisible = ref(false);
const testResult = ref<ConnectionTestResult | null>(null);

// 删除确认相关
const deleteDialogVisible = ref(false);
const deleteItem = ref<DataSource | null>(null);

// 删除确认内容 - 使用计算属性避免undefined显示
const deleteConfirmContent = computed(() => {
  if (!deleteItem.value || !deleteItem.value.name) {
    return '确定要删除该数据源吗？';
  }
  return `确定要删除数据源「${deleteItem.value.name}」吗？`;
});







// 新增数据源
const handleAdd = () => {
  drawerMessenger.showDrawer({
    title: '新增数据源',
    component: 'DataSourceForm',
    props: {
      isEdit: false
    },
    size: '28%'
  });
};

// 查看数据源详情
const handleView = async (row: DataSource) => {
  try {
    // 从后端获取完整的数据源信息
    const fullDataSource = await dataSourceService.getDataSourceById(row.id);
    if (!fullDataSource) {
      ElMessage.error('数据源不存在或已被删除');
      return;
    }

    drawerMessenger.showDrawer({
      title: '查看数据源',
      component: 'DataSourceForm',
      props: {
        isEdit: false, // 查看模式，不显示保存按钮
        isView: true,  // 标识为查看模式
        editData: fullDataSource // 使用从后端获取的完整数据
      },
      size: '28%'
    });
  } catch (error) {
    console.error('获取数据源详情失败:', error);
    ElMessage.error('获取数据源详情失败，请稍后重试');
  }
};

// 编辑数据源
const handleEdit = async (row: DataSource, event?: Event) => {
  // 阻止事件冒泡，防止触发卡片的点击事件
  if (event) {
    event.stopPropagation();
  }

  try {
    // 从后端获取完整的数据源信息
    const fullDataSource = await dataSourceService.getDataSourceById(row.id);
    if (!fullDataSource) {
      ElMessage.error('数据源不存在或已被删除');
      return;
    }

    drawerMessenger.showDrawer({
      title: '编辑数据源',
      component: 'DataSourceForm',
      props: {
        isEdit: true,
        editData: fullDataSource // 使用从后端获取的完整数据
      },
      size: '28%'
    });
  } catch (error) {
    console.error('获取数据源详情失败:', error);
    ElMessage.error('获取数据源详情失败，请稍后重试');
  }
};



// 测试连接（表格中的测试按钮）
const handleTest = async (row: DataSource, event?: Event) => {
  // 阻止事件冒泡，防止触发卡片的点击事件
  if (event) {
    event.stopPropagation();
  }
  try {
      testResult.value = null;
      testDialogVisible.value = true;

      // 使用新的API：根据ID测试已保存的数据源连接
      // 这样后端会自动获取数据库中的连接参数并解密密码
      const result = await dataSourceService.testSavedDataSourceConnection(row.id);
      console.log('测试连接结果:', result); // 添加调试日志
      testResult.value = result;
    } catch (error) {
      // 只有在网络错误或其他异常情况下才会到这里
      // 正常的测试失败应该通过testResult.success=false来处理
      console.error('测试连接请求失败:', error);
      testResult.value = {
        success: false,
        message: '网络请求失败',
        errorDetail: error.message || '无法连接到后端服务，请检查网络连接'
      };
    }
};



// 删除数据源
const handleDelete = (row: DataSource, event?: Event) => {
  // 阻止事件冒泡，防止触发卡片的点击事件
  if (event) {
    event.stopPropagation();
  }

  deleteItem.value = row;
  deleteDialogVisible.value = true;
};

// 取消删除
const cancelDelete = () => {
  deleteDialogVisible.value = false;
  deleteItem.value = null;
};

// 确认删除
const confirmDelete = async () => {
  if (!deleteItem.value) return;

  const itemName = deleteItem.value.name; // 保存名称，避免在异步操作中丢失

  try {
    const success = await dataSourceService.deleteDataSource(deleteItem.value.id);
    if (success) {
        handleSuccessMessage(`数据源「${itemName}」已删除`);
        // 删除操作：跳转第一页刷新（最高效）
        loadDataSourcesToFirstPage();
      } else {
        handleApiError(null, '删除数据源失败');
      }
  } catch (error) {
      handleApiError(error, '删除数据源失败');
    } finally {
    // 延迟清理，确保对话框完全关闭后再清理数据
    setTimeout(() => {
      deleteDialogVisible.value = false;
      deleteItem.value = null;
    }, 100);
  }
};

// 组件卸载时清理
onUnmounted(() => {
  pageRefreshUtil.unregisterRefresh(PAGE_KEYS.DATA_SOURCE);
});

// 页面加载时注册刷新机制
onMounted(() => {
  loadDataSources();

  // 使用工具类注册刷新机制
  pageRefreshUtil.registerRefresh(
    PAGE_KEYS.DATA_SOURCE,
    loadDataSources,           // 保持当前页刷新
    loadDataSourcesToFirstPage // 跳转第一页刷新
  );
});
</script>

<style lang="scss" scoped>
@use '@/assets/styles/page-common.scss' as *;
/* 引入公共样式，但保留现有样式作为备份和覆盖 */

/* 数据源名称列样式 */
.datasource-name {
  display: flex;
  align-items: center;
  font-weight: 400;
}

.datasource-name .el-icon {
  margin-right: 8px;
  font-size: 16px;
  color: #3FC8DD;
}

.db-name {
  font-size: 12px;
  color: #999;
  margin-top: 2px;
  font-weight: 400;
}

/* 状态标签 */
:deep(.el-tag) {
  font-weight: 400;
  padding: 2px 8px;
  border: 1px solid;
}

/* 测试结果样式 */
.test-result {
  padding: 20px;
  background: #f5f7fa;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.result-header {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 20px;
}

.result-header .el-icon {
  margin-right: 8px;
  font-size: 20px;
}

.result-header.success {
  color: #67c23a;
}

.result-header.error {
  color: #f56c6c;
}

.result-details {
  background: white;
  padding: 15px 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.detail-item {
  display: flex;
  margin: 8px 0;
  align-items: center;
}

.detail-item .label {
  font-weight: 500;
  width: 120px;
  color: #303133;
}

.detail-item .value {
  color: #606266;
  font-weight: 400;
}

.result-error {
  color: #f56c6c;
  background: #fef0f0;
  padding: 15px 20px;
  border: 1px solid #fbc4c4;
  border-radius: 4px;
  font-weight: 400;
}

/* 测试连接对话框样式 */
.test-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 20px;
  color: #606266;
  font-size: 14px;
}

.test-result {
  /* 确保测试结果容器不会截断内容 */
  width: 100%;
  max-width: none;
  overflow: visible;

  .result-header {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 16px;
    border-radius: 6px;
    margin-bottom: 16px;

    &.success {
      background: #f0f9ff;
      border: 1px solid #bae6fd;
      color: #0369a1;

      .el-icon {
        color: #10b981;
      }
    }

    &.error {
      background: #fef2f2;
      border: 1px solid #fecaca;
      color: #dc2626;

      .el-icon {
        color: #ef4444;
      }
    }

    .result-title {
      font-weight: 500;
      font-size: 16px;
    }
  }

  .result-content {
    .result-message {
      padding: 12px 16px;
      background: #f8f9fa;
      border-radius: 4px;
      margin-bottom: 12px;
      font-size: 14px;
      line-height: 1.6;
      word-wrap: break-word;
      word-break: break-word;
      white-space: pre-wrap;
      /* 确保内容完全显示，不被截断 */
      width: 100%;
      max-width: none;
      overflow: visible;
      text-overflow: clip;
      /* 移除任何可能的省略号样式 */
      -webkit-line-clamp: unset;
      -webkit-box-orient: unset;
      display: block;
    }

    .result-details {
      .detail-item {
        display: flex;
        justify-content: space-between;
        padding: 8px 16px;
        background: #f0f9ff;
        border-radius: 4px;
        margin-bottom: 8px;

        .label {
          color: #6b7280;
          font-size: 14px;
        }

        .value {
          color: #059669;
          font-weight: 500;
          font-size: 14px;
        }
      }
    }

    .result-error {
      .error-title {
        color: #dc2626;
        font-weight: 500;
        margin-bottom: 8px;
        font-size: 14px;
      }

      .error-content {
        padding: 12px;
        background: #fef2f2;
        border: 1px solid #fecaca;
        border-radius: 4px;
        color: #7f1d1d;
        font-size: 13px;
        line-height: 1.5;
        word-break: break-word;
      }
    }
  }
}

/* 视图切换按钮样式 - 移到左侧，更明显 */
.view-toggle {
  margin-right: 16px;

  .el-button-group {
    .el-button {
      // 移除自定义padding，使用Element Plus默认的default尺寸
      border-radius: 6px;
      background-color: #ffffff; /* 未激活状态使用白色背景 */
      border-color: #dcdfe6; /* 浅灰色边框 */
      color: #606266; /* 深灰色文字 */

      &:first-child {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
      }

      &:last-child {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
      }

      .el-icon {
        font-size: 18px;
      }

      &:hover:not(.el-button--primary) {
        background-color: #f5f7fa; /* 悬停时浅灰色背景 */
        border-color: #c0c4cc;
        color: #409eff;
      }

      &.el-button--primary {
        background-color: #3FC8DD; /* 与搜索框和分页组件主题色一致 */
        border-color: #3FC8DD;
        color: #ffffff;
        box-shadow: 0 2px 8px rgba(63, 200, 221, 0.3); /* 调整阴影颜色 */

        &:hover {
          background-color: #36B3CC; /* 悬停时稍微深一点的青色 */
          border-color: #36B3CC;
        }
      }
    }
  }
}

/* 卡片视图样式 */
.card-view {
  position: relative;

  // 为Glassmorphism样式提供渐变背景
  &::before {
    content: '';
    position: absolute;
    top: -20px;
    left: -20px;
    right: -20px;
    bottom: -20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    opacity: 0.05;
    border-radius: 20px;
    z-index: -1;
  }

  .card-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr)); // 减小最小宽度，便于每行显示更多卡片
    gap: 16px;
    margin-bottom: 24px;
    padding: 16px 0;

    // 手机端：1列
    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 12px;
    }

    // 平板端：2-3列
    @media (min-width: 769px) and (max-width: 1024px) {
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: 14px;
    }

    // 桌面端：3-4列
    @media (min-width: 1025px) and (max-width: 1400px) {
      grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
      gap: 16px;
    }

    // 大屏幕：理想的5列布局
    @media (min-width: 1401px) {
      grid-template-columns: repeat(5, 1fr); // 固定5列，自动适配宽度
      gap: 18px;
    }

    // 超大屏幕：保持5列但增加间距
    @media (min-width: 1800px) {
      grid-template-columns: repeat(5, 1fr);
      gap: 20px;
      max-width: 1600px; // 限制最大宽度，避免卡片过宽
      margin: 0 auto 24px;
    }
  }

  .card-empty {
    margin: 40px 0;
  }

  .card-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    margin-top: 3px; // 增加上边距，让按钮下移3px
    margin-bottom: 6px; // 距离底部6px（原3px + 下移3px）
  }

  // 强制确保所有卡片都有边框
  :deep(.base-card) {
    border: 1px solid #e4e7ed !important;

    &:hover {
      border-color: #c0c4cc !important;
    }

    // 对于移除了边框的样式变体，重新添加边框
    &.style-card-elevated {
      border: 1px solid #e4e7ed !important;

      &:hover {
        border-color: #c0c4cc !important;
      }
    }
  }
}


/* 数据源卡片内容样式 - 精细化设计 */
.datasource-card-content {
  padding: 0;

  // 卡片头部：标题 + 类型标签
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 8px;

    .datasource-title {
      margin: 0;
      margin-top: -2px; // 上移2px
      font-size: 16px; // 字体调大
      font-weight: normal; // 去掉粗体，使用正常字重
      color: #303133;
      line-height: 1.2;
      flex: 1;
      margin-right: 6px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      text-align: left; // 确保左对齐
    }

    .db-type-tag {
      flex-shrink: 0;
      font-size: 10px;
      height: 18px;
      line-height: 16px;
      padding: 0 5px;
      border-radius: 9px;
      font-weight: 500;
    }
  }

  // 连接信息区域 - 紧凑设计
  .connection-info {
    margin-bottom: 8px;

    .info-row {
      display: flex;
      flex-direction: column;
      gap: 4px;
    }

    .info-item {
      display: flex;
      align-items: center;
      font-size: 13px; // 字体稍大
      color: #606266;

      .info-icon {
        margin-right: 6px; // 图标与文字距离稍近
        font-size: 14px;
        color: #909399;
        flex-shrink: 0;
      }

      .info-text {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        flex: 1;
        font-weight: 500;
        text-align: left; // 确保左对齐
      }
    }
  }

  // 状态栏 - 精简设计
  .status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;

    .status-tag {
      display: flex;
      align-items: center;
      font-size: 10px;
      height: 18px;
      padding: 0 6px;
      font-weight: 500;

      .status-icon {
        margin-right: 3px;
        font-size: 10px;
      }
    }

    .update-time {
      display: flex;
      align-items: center;
      font-size: 10px;
      color: #909399;

      .time-icon {
        margin-right: 3px;
        font-size: 10px;
      }

      .time-text {
        white-space: nowrap;
        font-weight: 500;
      }
    }
  }

  // 操作按钮区域 - 紧凑布局，按钮下移3px
  .card-actions {
    margin-top: 11px; // 增加上边距，让按钮下移3px
    margin-bottom: 6px; // 距离底部6px（原3px + 下移3px）
    display: flex;
    justify-content: flex-end;
    gap: 6px;
    padding-top: 8px;
    border-top: 1px solid #f5f5f5;

    .el-button {
      font-size: 11px;
      padding: 3px 8px;
      height: 24px;
      border-radius: 4px;
    }
  }
}

/* 响应式设计 - 2024-12-26: 部分已在page-common.scss中，保留页面特有的响应式样式 */
@media (max-width: 768px) {
  .page-container {
    margin: 3px;
    padding: 12px;
  }

  .action-bar {
    flex-direction: column;
    gap: 12px;
  }

  .search-input {
    width: 100%;
  }

  .action-buttons {
    justify-content: center;
  }
}


</style>