/**
 * 接口配置服务
 *
 * 注意：Mock 服务已禁用，强制使用 API 服务
 * 所有数据通过 apiClient 自动处理字段转换和时间格式化
 */

import type {
  InterfaceConfig,
  InterfaceConfigRequest,
  InterfaceConfigListResponse,
  InterfaceConfigQuery,
  TableStructure
} from '@/types/interface-config';
import { apiClient } from '@/utils/http-client';
// Mock 相关导入已禁用，强制使用 API 服务
// import {
//   mockGetInterfaceConfigs,
//   mockCreateInterfaceConfig,
//   mockUpdateInterfaceConfig,
//   mockDeleteInterfaceConfig,
//   mockGetTableStructure
// } from '@/mock/interface-config.mock';

/**
 * 接口配置服务接口
 */
interface IInterfaceConfigService {
  getInterfaceConfigs(query?: InterfaceConfigQuery): Promise<InterfaceConfigListResponse>;
  getInterfaceConfigById(id: number): Promise<InterfaceConfig | undefined>;
  createInterfaceConfig(data: InterfaceConfigRequest): Promise<InterfaceConfig>;
  updateInterfaceConfig(id: number, data: InterfaceConfigRequest): Promise<InterfaceConfig>;
  deleteInterfaceConfig(id: number): Promise<boolean>;
  getTableStructure(datasourceId: number, tableName: string): Promise<TableStructure>;
  checkPathExists(path: string, method: string, excludeId?: number): Promise<boolean>;
}

/**
 * Mock接口配置服务实现（已禁用，强制使用API服务）
 */
// class MockInterfaceConfigService implements IInterfaceConfigService {
//   async getInterfaceConfigs(query: InterfaceConfigQuery = {}): Promise<InterfaceConfigListResponse> {
//     const { page = 1, page_size = 10 } = query;
//     return mockGetInterfaceConfigs(page, page_size, query);
//   }
//
//   async getInterfaceConfigById(id: number): Promise<InterfaceConfig | undefined> {
//     const response = await mockGetInterfaceConfigs(1, 1000); // 获取所有数据
//     return response.items.find(config => config.id === id);
//   }
//
//   async createInterfaceConfig(data: InterfaceConfigRequest): Promise<InterfaceConfig> {
//     return mockCreateInterfaceConfig(data);
//   }
//
//   async updateInterfaceConfig(id: number, data: InterfaceConfigRequest): Promise<InterfaceConfig> {
//     return mockUpdateInterfaceConfig(id, data);
//   }
//
//   async deleteInterfaceConfig(id: number): Promise<boolean> {
//     return mockDeleteInterfaceConfig(id);
//   }

//   async getTableStructure(datasourceId: number, tableName: string): Promise<TableStructure> {
//     return mockGetTableStructure(datasourceId, tableName);
//   }
//
//   async checkPathExists(path: string, method: string, excludeId?: number): Promise<boolean> {
//     const response = await mockGetInterfaceConfigs(1, 1000);
//     return response.items.some(config =>
//       config.path === path &&
//       config.method === method &&
//       (!excludeId || config.id !== excludeId)
//     );
//   }
// }

/**
 * API接口配置服务实现
 */
class ApiInterfaceConfigService implements IInterfaceConfigService {
  async getInterfaceConfigs(query: InterfaceConfigQuery = {}): Promise<InterfaceConfigListResponse> {
    const { page = 1, page_size = 10, search, groupId, datasourceId, method, isEnabled, isPublic, tagIds } = query;

    const params: Record<string, any> = {
      page,
      size: page_size
    };

    if (search) params.search = search;
    if (groupId) params.group_id = groupId;
    if (datasourceId) params.datasource_id = datasourceId;
    if (method) params.method = method;
    if (isEnabled !== undefined) params.is_enabled = isEnabled;
    if (isPublic !== undefined) params.is_public = isPublic;
    if (tagIds && tagIds.length > 0) params.tag_ids = tagIds;

    return await apiClient.get<InterfaceConfigListResponse>('/interface/configs', params);
  }
  
  async getInterfaceConfigById(id: number): Promise<InterfaceConfig | undefined> {
    try {
      return await apiClient.get<InterfaceConfig>(`/interface/configs/${id}`);
    } catch (error) {
      return undefined;
    }
  }

  async createInterfaceConfig(data: InterfaceConfigRequest): Promise<InterfaceConfig> {
    console.log('=== API服务调试 ===');
    console.log('请求URL: /interface/configs');
    console.log('请求数据:', JSON.stringify(data, null, 2));
    console.log('apiClient baseURL:', '/api/v1');

    try {
      const result = await apiClient.post<InterfaceConfig>('/interface/configs', data);
      console.log('API响应成功:', result);
      return result;
    } catch (error) {
      console.error('API请求失败:', error);
      throw error;
    }
  }

  async updateInterfaceConfig(id: number, data: InterfaceConfigRequest): Promise<InterfaceConfig> {
    return await apiClient.put<InterfaceConfig>(`/interface/configs/${id}`, data);
  }
  
  async deleteInterfaceConfig(id: number): Promise<boolean> {
    await apiClient.delete(`/interface/configs/${id}`);
    return true;
  }

  async getTableStructure(datasourceId: number, tableName: string): Promise<TableStructure> {
    return await apiClient.get<TableStructure>(`/datasources/${datasourceId}/tables/${tableName}/structure`);
  }

  async checkPathExists(path: string, method: string, excludeId?: number): Promise<boolean> {
    const params: Record<string, any> = {
      path,
      method
    };

    if (excludeId) {
      params.exclude_id = excludeId;
    }

    const result = await apiClient.get<{ exists: boolean }>('/interface/configs/check-path', params);
    return result.exists;
  }
}

// 接口配置服务强制使用API模式，Mock已禁用
const interfaceConfigService: IInterfaceConfigService = new ApiInterfaceConfigService();

export default interfaceConfigService;
