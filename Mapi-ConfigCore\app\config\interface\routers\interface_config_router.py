"""
接口配置路由层
定义API路由和端点
"""

from fastapi import APIRouter, Depends, Query, Path, Request
from typing import Optional
from app.config.interface.controllers.interface_config_controller import InterfaceConfigController
from app.config.interface.schemas.interface_config_schema import (
    InterfaceConfigCreate,
    InterfaceConfigUpdate,
    InterfaceConfigResponse,
    InterfaceConfigListResponse
)

# 创建路由器
router = APIRouter()

@router.get("/", response_model=InterfaceConfigListResponse, summary="获取接口配置列表")
async def get_interface_configs(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页大小"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    group_id: Optional[int] = Query(None, description="分组ID过滤"),
    method: Optional[str] = Query(None, description="HTTP方法过滤"),
    is_enabled: Optional[bool] = Query(None, description="启用状态过滤"),
    controller: InterfaceConfigController = Depends()
):
    """
    获取接口配置列表（分页）

    支持的查询参数：
    - **page**: 页码，从1开始
    - **size**: 每页大小，1-100之间
    - **search**: 搜索关键词，支持名称、路径、描述模糊搜索
    - **group_id**: 分组ID过滤，只显示指定分组的接口
    - **method**: HTTP方法过滤，如GET、POST等
    - **is_enabled**: 启用状态过滤，true/false

    返回分页的接口配置列表，包含总数、页数等信息
    """
    return await controller.get_interface_configs(page, size, search, group_id, method, is_enabled)


@router.post("/", response_model=InterfaceConfigResponse, summary="创建接口配置")
async def create_interface_config(
    request: Request,
    config_data: InterfaceConfigCreate,
    controller: InterfaceConfigController = Depends()
):
    """
    创建新的接口配置

    请求体参数：
    - **name**: 接口名称，必填，1-100字符
    - **path**: 接口路径，必填，1-200字符，必须以/开头
    - **method**: HTTP方法，必填，GET/POST/PUT/DELETE/PATCH
    - **description**: 接口描述，可选
    - **group_id**: 所属分组ID，必填
    - **datasource_id**: 数据源ID，必填
    - **table_name**: 数据表名，必填，1-100字符
    - **is_enabled**: 是否启用，默认true
    - **is_public**: 是否公开(无需认证)，默认false
    - **query_fields**: 可查询字段列表，可选
    - **required_fields**: 必填字段列表，可选
    - **response_fields**: 响应字段列表，可选
    - **cache_duration**: 缓存时长(秒)，默认300，0-3600
    - **rate_limit**: 速率限制(次/分钟)，默认100，1-10000
    - **tags**: 标签ID列表，可选
    - **created_by**: 创建人，可选

    返回创建的接口配置信息
    """
    result = await controller.create_interface_config(config_data)
    return result


@router.get("/{config_id}", response_model=InterfaceConfigResponse, summary="获取单个接口配置")
async def get_interface_config(
    config_id: int = Path(..., description="接口配置ID"),
    controller: InterfaceConfigController = Depends()
):
    """
    根据ID获取单个接口配置详情

    路径参数：
    - **config_id**: 接口配置ID

    返回接口配置详细信息，包含关联的分组、数据源、标签等信息
    """
    return await controller.get_interface_config(config_id)


@router.put("/{config_id}", response_model=InterfaceConfigResponse, summary="更新接口配置")
async def update_interface_config(
    request: Request,
    config_data: InterfaceConfigUpdate,
    config_id: int = Path(..., description="接口配置ID"),
    controller: InterfaceConfigController = Depends()
):
    """
    更新接口配置信息

    路径参数：
    - **config_id**: 要更新的接口配置ID

    请求体参数（所有参数都是可选的）：
    - **name**: 接口名称，1-100字符
    - **path**: 接口路径，1-200字符，必须以/开头
    - **method**: HTTP方法，GET/POST/PUT/DELETE/PATCH
    - **description**: 接口描述
    - **group_id**: 所属分组ID
    - **datasource_id**: 数据源ID
    - **table_name**: 数据表名，1-100字符
    - **is_enabled**: 是否启用
    - **is_public**: 是否公开(无需认证)
    - **query_fields**: 可查询字段列表
    - **required_fields**: 必填字段列表
    - **response_fields**: 响应字段列表
    - **cache_duration**: 缓存时长(秒)，0-3600
    - **rate_limit**: 速率限制(次/分钟)，1-10000
    - **tags**: 标签ID列表

    返回更新后的接口配置信息
    """
    result = await controller.update_interface_config(config_id, config_data)
    return result


@router.delete("/{config_id}", summary="删除接口配置")
async def delete_interface_config(
    config_id: int = Path(..., description="接口配置ID"),
    controller: InterfaceConfigController = Depends()
):
    """
    删除接口配置

    路径参数：
    - **config_id**: 要删除的接口配置ID

    删除接口配置会同时删除相关的标签关联关系
    """
    return await controller.delete_interface_config(config_id)
