<template>
  <div class="drawer-content drawer-form-content drawer-form drawer-select">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      label-position="left"
    >
      <el-form-item label="数据源名称" prop="name" required>
        <el-input
          v-model="formData.name"
          placeholder="请输入数据源名称"
          :disabled="loading || isReadOnly"
        />
      </el-form-item>

      <el-form-item label="描述信息" prop="description">
        <el-input
          v-model="formData.description"
          placeholder="请输入数据源描述信息（可选）"
          :disabled="loading || isReadOnly"
          type="textarea"
          :rows="2"
        />
      </el-form-item>

      <el-form-item label="数据库类型" prop="dbType" required>
        <el-select
          v-model="formData.dbType"
          placeholder="--请选择--"
          style="width: 100%"
          :disabled="loading || isReadOnly"
          @change="handleDbTypeChange"
          :teleported="true"
          popper-class="high-z-index-popper"
        >
          <el-option label="MySQL" value="mysql" />
          <el-option label="PostgreSQL" value="postgresql" />
          <el-option label="SQLite" value="sqlite" />
          <el-option label="SQL Server" value="sqlserver" />
          <el-option label="Oracle" value="oracle" />
        </el-select>

      </el-form-item>

      <el-form-item label="主机地址" prop="host" required>
        <el-input
          v-model="formData.host"
          placeholder="请输入数据库主机地址"
          :disabled="loading || isReadOnly"
        />
      </el-form-item>

      <el-form-item label="端口" prop="port" required>
        <el-input-number
          v-model="formData.port"
          :min="1"
          :max="65535"
          controls-position="right"
          style="width: 100%"
          :disabled="loading || isReadOnly"
        />
      </el-form-item>

      <el-form-item label="数据库名" prop="database" required>
        <el-input
          v-model="formData.database"
          placeholder="请输入数据库名称"
          :disabled="loading || isReadOnly"
        />
      </el-form-item>

      <el-form-item label="用户名" prop="username" required>
        <el-input
          v-model="formData.username"
          placeholder="请输入数据库用户名"
          :disabled="loading || isReadOnly"
        />
      </el-form-item>

      <el-form-item label="密码" prop="password" required>
        <el-input
          v-model="formData.password"
          type="password"
          :placeholder="isReadOnly ? '加密密码' : (isEdit ? '显示加密密码，如需修改请输入新密码' : '请输入数据库密码')"
          show-password
          :disabled="loading || isReadOnly"
        />
        <div v-if="isEdit && !isReadOnly" class="form-item-tip">
          当前显示的是加密后的密码。如需修改密码，请清空并输入新密码；如不修改，保持原内容不变。
        </div>
      </el-form-item>

      <el-form-item label="最大连接数" prop="maxConnections">
        <el-input-number
          v-model="formData.maxConnections"
          :min="1"
          :max="100"
          controls-position="right"
          style="width: 100%"
          :disabled="loading || isReadOnly"
        />
      </el-form-item>

      <el-form-item label="连接超时" prop="connectionTimeout">
        <el-input-number
          v-model="formData.connectionTimeout"
          :min="5"
          :max="300"
          controls-position="right"
          style="width: 100%"
          :disabled="loading || isReadOnly"
        />
        <div class="form-item-tip">连接超时时间（秒），建议30-120秒</div>
      </el-form-item>
    </el-form>

    <!-- 移除测试连接功能，只保留保存功能 -->
  </div>

  <!-- 底部按钮已抽象到MainIndex中统一管理 -->

  <!-- 测试连接结果现在显示在抽屉内部 -->
</template>

<script setup lang="ts">
import { reactive, ref, computed, onMounted, watch } from 'vue';
import { ElMessage, type FormInstance } from 'element-plus';
import dataSourceService from '@/services/datasource.service';
import type { DataSourceRequest } from '@/types/datasource';
import { useGlobalDrawerMessenger } from '@/utils/piniaMessenger';
import { PageRefresh } from '@/utils/pageRefreshUtil';
import { extractErrorMessage } from '@/utils/common-utils';
import { useGlobalDrawerStore } from '@/stores/globalDrawerStore';
import { PasswordUtils } from '@/utils/common-utils';


// 全局抽屉通信助手
const drawerMessenger = useGlobalDrawerMessenger();

// 全局抽屉状态管理
const drawerStore = useGlobalDrawerStore();

// 表单引用
const formRef = ref<FormInstance>();

// 加载状态
const loading = ref(false);

// 表单数据
const formData = reactive<DataSourceRequest>({
  name: '',
  description: '',
  dbType: 'mysql',
  host: '',
  port: 3306,
  database: '',
  username: '',
  password: '',
  maxConnections: 10,
  connectionTimeout: 60,
  createdBy: 'admin'
});

// 计算属性 - 从抽屉store中获取数据
const isEdit = computed(() => drawerStore.props.isEdit || false);
const isView = computed(() => drawerStore.props.isView || false);
const editData = computed(() => drawerStore.props.editData || null);

// 确保编辑模式和查看模式互斥
const isReadOnly = computed(() => isView.value && !isEdit.value);

// 更新抽屉底部按钮配置
const updateDrawerButtons = () => {
  const leftButtons: any[] = [
    // 移除测试连接按钮，只保留保存功能
  ];

  const rightButtons = [];

  // 查看模式只显示关闭按钮
  if (isReadOnly.value) {
    rightButtons.push({
      text: '关闭',
      handler: handleCancel
    });
  } else {
    // 编辑/新增模式显示取消和保存按钮
    rightButtons.push(
      {
        text: '取消',
        handler: handleCancel
      },
      {
        text: isEdit.value ? '更新' : '创建',
        type: 'primary' as const,
        handler: handleSubmit,
        loading: loading.value,
        loadingText: '保存中...'
      }
    );
  }

  // 更新store中的按钮配置
  drawerStore.leftButtons = leftButtons;
  drawerStore.rightButtons = rightButtons;
};

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入数据源名称', trigger: 'blur' },
    { min: 1, max: 50, message: '数据源名称长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  dbType: [
    { required: true, message: '请选择数据库类型', trigger: 'change' }
  ],
  host: [
    { required: true, message: '请输入主机地址', trigger: 'blur' }
  ],
  port: [
    { required: true, message: '请输入端口号', trigger: 'blur' },
    { type: 'number', min: 1, max: 65535, message: '端口号必须在 1-65535 之间', trigger: 'blur' }
  ],
  database: [
    { required: true, message: '请输入数据库名', trigger: 'blur' }
  ],
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ],
  maxConnections: [
    { required: true, message: '请输入最大连接数', trigger: 'blur' },
    { type: 'number', min: 1, max: 100, message: '最大连接数必须在 1-100 之间', trigger: 'blur' }
  ]
};

// 数据库类型变化处理
const handleDbTypeChange = (dbType: string) => {
  // 根据数据库类型设置默认端口
  const defaultPorts: Record<string, number> = {
    mysql: 3306,
    postgresql: 5432,
    sqlserver: 1433,
    oracle: 1521,
    sqlite: 0
  };

  if (defaultPorts[dbType] !== undefined) {
    formData.port = defaultPorts[dbType];
  }
};

// 移除测试连接功能，用户直接保存数据源
// 保存后可以在列表页面测试连接

// 取消操作
const handleCancel = () => {
  drawerMessenger.hideDrawer();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    // 表单验证
    await formRef.value.validate();

    loading.value = true;

    if (isEdit.value && editData.value) {
      // 编辑模式 - 直接发送所有数据，后端会智能处理密码
      await dataSourceService.updateDataSource(editData.value.id, formData);
      ElMessage.success('数据源更新成功');

      // 修改操作：保持当前页刷新
      PageRefresh.dataSource.afterEdit();
    } else {
      // 创建模式 - 密码必填
      if (!formData.password || formData.password.trim() === '') {
        ElMessage.error('创建数据源时密码不能为空');
        return;
      }

      await dataSourceService.createDataSource(formData);
      ElMessage.success('数据源创建成功');

      // 新增操作：跳转第一页刷新
      PageRefresh.dataSource.afterAdd();
    }

    // 关闭抽屉
    drawerMessenger.hideDrawer();
  } catch (error: any) {
    console.error('保存失败:', error);
    ElMessage.error(extractErrorMessage(error, '保存失败'));
  } finally {
    loading.value = false;
  }
};

// 初始化数据
const initFormData = () => {
  if ((isEdit.value || isView.value) && editData.value) {
    // 使用密码工具类处理密码字段（直接返回原数据）
    const processedData = PasswordUtils.processDataSourcePassword(editData.value);

    Object.assign(formData, {
      name: processedData.name || '',
      description: processedData.description || '',
      dbType: processedData.dbType || 'mysql',
      host: processedData.host || '',
      port: processedData.port || 3306,
      database: processedData.database || '',
      username: processedData.username || '',
      password: processedData.password || '', // 显示加密密码
      maxConnections: processedData.maxConnections || 10,
      connectionTimeout: processedData.connectionTimeout || 60,
      createdBy: processedData.createdBy || 'admin'
    });
  }
};

// 监听抽屉store中的数据变化，确保编辑数据正确加载
watch(() => drawerStore.props, (newProps) => {
  if (newProps && newProps.editData) {
    initFormData();
  }
  updateDrawerButtons(); // 更新按钮配置
}, { immediate: true, deep: true });

// 监听加载状态变化，更新按钮
watch([loading, isEdit, isReadOnly], () => {
  updateDrawerButtons();
});

// 组件挂载时初始化
onMounted(() => {
  initFormData();
  updateDrawerButtons(); // 初始化按钮配置
});


</script>

<style lang="scss" scoped>
@use '@/assets/styles/page-common.scss' as *;
/* 2024-12-27: 底部按钮样式已移至 page-common.scss，使用公共样式 */
.drawer-content {
  flex: 1;
}

/* 表单提示文本样式 */
.form-item-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

/* 测试结果样式 */
.test-result {
  .test-result-header {
    display: flex;
    align-items: center;
    margin-bottom: 16px;

    .test-result-title {
      margin-left: 8px;
      font-size: 16px;
      font-weight: 500;
    }
  }

  .test-result-details {
    background: #f0f9ff;
    border: 1px solid #e1f5fe;
    border-radius: 4px;
    padding: 12px;

    p {
      margin: 0 0 8px 0;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .test-result-error {
    background: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 4px;
    padding: 12px;

    p {
      margin: 0 0 8px 0;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .error-message {
      color: #dc2626;
      font-family: monospace;
      background: #fff;
      padding: 8px;
      border-radius: 4px;
      border: 1px solid #f87171;
    }
  }
}

/* 测试结果样式 */
.test-result-section {
  margin-top: 16px;
}

/* 移除测试连接相关样式 */
</style>
