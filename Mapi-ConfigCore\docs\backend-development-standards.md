---
type: "always_apply"
description: "后端开发规范 - Python/FastAPI分层架构、日志系统、异常处理、接口参数配置等完整规范"
---

# 后端开发规范

## 🎯 核心原则

### 1. 架构分层
- 严格按照 Router → Controller → Service → Repository → Model 分层
- 每层只负责自己的职责，不允许跨层调用
- 统一使用 snake_case 字段命名

### 2. 调试规范 ⚠️ 强制执行
- **禁止提交任何 print() 调试语句**
- **禁止提交 console.log 等前端调试代码**
- **调试完成后必须清理所有调试信息**
- **使用 LogUtil 日志系统进行调试和记录**

### 3. 日志系统规范 ⚠️ 强制执行
- **日志绝对不允许阻塞业务流程**
- **日志出错最多记录不了，不能导致业务终止**
- **所有日志调用必须用 try-catch 包装或确保 LogUtil 正确导入**
- **调试时可以添加日志，但调试结束后必须移除或注释掉调试日志**

## 🏗️ 分层架构规范

### 1. Router层（路由层）
**职责**：定义API路由和端点

```python
@router.get("/", response_model=InterfaceGroupListResponse)
async def get_interface_groups(
    page: int = Query(1, ge=1, description="页码"),
    controller: InterfaceGroupController = Depends()
):
    return await controller.get_interface_groups(page, size, search)
```

**规范**：
- ✅ 只负责路由定义和API文档
- ✅ 参数验证（使用FastAPI的Query、Path等）
- ✅ 直接调用Controller层方法
- ❌ 不允许包含业务逻辑
- ❌ 不允许直接操作数据库
- ❌ 不允许进行数据转换

### 2. Controller层（控制器层）
**职责**：处理HTTP请求和响应

```python
async def get_interface_groups(self, page: int, size: int, search: str):
    return self.service.get_interface_groups(page, size, search)
```

**规范**：
- ✅ 只负责HTTP请求处理
- ✅ 参数接收和传递
- ✅ 直接调用Service层方法
- ❌ 不允许包含业务逻辑
- ❌ 不允许直接操作数据库
- ❌ 不允许进行数据转换

### 3. Service层（业务逻辑层）
**职责**：处理业务逻辑

```python
def get_interface_groups(self, page: int, size: int, search: str):
    # 业务验证
    if page < 1:
        raise BusinessException("页码必须大于0")
    
    # 获取数据
    items, total = self.repository.get_list(page, size, search)
    
    # 转换为响应格式（使用from_orm）
    groups = []
    for item in items:
        group_response = InterfaceGroupResponse.from_orm(item)
        groups.append(group_response)
    
    return InterfaceGroupListResponse(items=groups, total=total, ...)
```

**规范**：
- ✅ 包含所有业务逻辑和验证
- ✅ 调用Repository层进行数据操作
- ✅ 使用`Schema.from_orm()`进行数据转换
- ✅ 异常处理和日志记录
- ❌ 不允许手动进行字段映射
- ❌ 不允许直接操作数据库

### 4. Repository层（数据访问层）
**职责**：数据库操作

```python
def get_list(self, page: int, size: int, search: str):
    query = self.db.query(InterfaceGroupModel)
    if search:
        query = query.filter(InterfaceGroupModel.name.ilike(f"%{search}%"))
    
    total = query.count()
    items = query.offset((page - 1) * size).limit(size).all()
    return items, total
```

**规范**：
- ✅ 直接操作数据库和ORM模型
- ✅ 返回Model对象给Service层
- ✅ 包含复杂查询逻辑
- ❌ 不允许包含业务逻辑
- ❌ 不允许进行数据转换

### 5. Schema层（数据模式层）
**职责**：定义数据结构和验证规则

```python
class InterfaceGroupResponse(InterfaceGroupBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    interface_count: Optional[int] = Field(None, description="包含的接口数量")
    created_at: datetime
    updated_at: datetime
    created_by: Optional[str]
```

**规范**：
- ✅ 统一使用snake_case字段命名
- ✅ 配置`from_attributes=True`支持ORM转换
- ✅ 完整的字段验证和文档
- ❌ 不允许使用camelCase字段名
- ❌ 不允许在Schema中进行字段转换

### 6. Model层（数据模型层）
**职责**：定义数据库表结构

```python
class InterfaceGroupModel(Base):
    __tablename__ = "interface_groups"
    
    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    path_prefix = Column(String(50), nullable=False)  # snake_case
    is_enabled = Column(Boolean, default=True)        # snake_case
    created_at = Column(DateTime, default=func.now()) # snake_case
```

**规范**：
- ✅ 统一使用snake_case字段命名
- ✅ 与数据库表结构保持一致
- ✅ 不包含任何业务逻辑
- ❌ 不允许在Model中进行字段转换
- ❌ 不允许为了"前端兼容"而使用camelCase
- ❌ **禁止使用 to_dict 方法进行字段转换**

## 📝 字段命名规范

### 1. 命名约定
- **后端：** 严格使用下划线命名 (`orm_model_config`, `parameter_config`)
- **前端：** 使用驼峰命名 (`ormModelConfig`, `parameterConfig`)
- **转换：** 前端通过 `common-utils` 工具自动转换
- **禁止：** 后端Schema中添加alias进行驼峰转换

### 2. 响应格式规范
```json
{
  "success": true/false,
  "message": "操作结果描述",
  "error_code": "RESOURCE_CONFLICT/RESOURCE_NOT_FOUND/VALIDATION_ERROR",
  "detail": {
    "具体的错误详情": "包含建议和相关信息"
  }
}
```

### 3. HTTP状态码规范
- **200 OK：** 操作成功
- **404 Not Found：** 资源不存在
- **409 Conflict：** 资源冲突（如删除时有关联）
- **422 Unprocessable Entity：** 验证失败
- **500 Internal Server Error：** 服务器内部错误

## 🔧 接口参数配置规范

### 1. 参数分类标准

**参考 Postman/Apifox 的参数结构，将ORM模型配置和接口参数配置分离**

#### Path Parameters (路径参数)
```json
{
  "type": "path",
  "name": "id",
  "description": "用户ID",
  "required": true,
  "dataType": "integer",
  "example": "123"
}
```

#### Query Parameters (查询参数)
**系统级参数：**
```json
{
  "type": "query",
  "category": "system",
  "parameters": [
    {
      "name": "page",
      "description": "页码",
      "required": false,
      "dataType": "integer",
      "defaultValue": 1,
      "example": "1"
    },
    {
      "name": "keyword",
      "description": "模糊查询关键词",
      "required": false,
      "dataType": "string",
      "example": "张三"
    }
  ]
}
```

**业务查询参数：**
```json
{
  "type": "query",
  "category": "business",
  "parameters": [
    {
      "name": "status",
      "description": "状态筛选",
      "required": false,
      "dataType": "enum",
      "options": ["active", "inactive", "pending"],
      "example": "active"
    }
  ]
}
```

#### Header Parameters (请求头参数)
```json
{
  "type": "header",
  "parameters": [
    {
      "name": "Authorization",
      "description": "认证令牌",
      "required": true,
      "dataType": "string",
      "example": "Bearer eyJhbGciOiJIUzI1NiIs..."
    }
  ]
}
```

#### Body Parameters (请求体参数)
```json
{
  "type": "body",
  "contentType": "application/json",
  "schema": {
    "type": "object",
    "properties": {
      "name": {
        "type": "string",
        "description": "用户名",
        "required": true,
        "example": "张三"
      }
    }
  }
}
```

### 2. ORM模型配置 vs 接口参数配置

**ORM模型配置：**
- 作用：定义数据表字段映射关系
- 内容：字段类型、字段别名、数据转换规则
- 示例：`user_name` -> `userName`, `created_at` -> `createdTime`

**接口参数配置：**
- 作用：定义接口的输入参数结构
- 内容：参数验证、参数类型、默认值、示例值
- 示例：分页参数、查询条件、认证信息

## 🚨 异常处理规范

### 1. 异常类型
```python
# 业务异常 - 给前端用户看，记录到biz日志
raise BusinessException(
    error_type=ErrorType.资源冲突,
    user_message="数据源仍有接口在使用，无法删除",
    detail={
        "suggestion": "请先删除或修改相关的接口配置",
        "related_items": interface_names
    }
)

# 技术异常 - 给开发者看，记录到tech日志
raise TechnicalException(
    error_type=ErrorType.数据库错误,
    developer_detail={
        "operation": "删除数据源",
        "error_detail": str(e),
        "business_context": "用户删除数据源时发生数据库异常"
    }
)
```

### 2. 日志记录规范
```python
# 使用LogUtil记录日志，包含6个独立文件：
# - debug_*.log - 调试日志，只记录DEBUG级别
# - info_*.log - 信息日志，只记录INFO级别  
# - warning_*.log - 警告日志，只记录WARNING级别
# - error_*.log - 错误日志，只记录ERROR级别
# - biz_*.log - 业务异常日志，由异常处理系统调用
# - tech_*.log - 技术异常日志，由异常处理系统调用

try:
    LogUtil.info("开始处理业务逻辑", user_id=user_id, operation="create")
    # 业务逻辑
    LogUtil.info("业务处理完成", result="success")
except Exception as e:
    LogUtil.error("业务处理失败", error=str(e), context="详细上下文")
    raise
```

## 🧪 测试规范

### 1. 测试文件组织
- **绝对不允许在test层外添加任何测试文件**
- **测试完成后必须删除不需要的文件和代码**
- 测试文件应放在 `tests/` 目录下
- 按模块组织测试文件结构

### 2. 测试类型
```python
# 单元测试
def test_create_interface_group():
    # 测试Service层业务逻辑
    pass

# 集成测试  
def test_interface_group_api():
    # 测试完整的API流程
    pass
```

## 🔄 数据转换规范

### 1. 正确的转换方式
```python
# ✅ 正确：使用from_orm自动映射
group_response = InterfaceGroupResponse.from_orm(group)

# ❌ 错误：手动字段映射
return {
    'groupId': group.group_id,  # 不要这样做
    'pathPrefix': group.path_prefix
}
```

### 2. 避免的错误模式
```python
# ❌ 错误：在Controller中包含业务逻辑
async def create_interface_group(self, group_data):
    if self.repository.get_by_name(group_data.name):  # 不应该在Controller中
        raise Exception("名称已存在")
    return self.repository.create(group_data)

# ❌ 错误：Model中转换为camelCase
def to_dict(self):
    return {
        'pathPrefix': self.path_prefix,  # 错误的camelCase转换
        'isEnabled': self.is_enabled
    }
```

## ✅ 检查清单

### 代码审查检查点
- [ ] 是否有层次职责混乱
- [ ] 是否有字段命名混用
- [ ] 是否有手动字段映射
- [ ] 是否正确使用from_orm
- [ ] API输出是否统一snake_case
- [ ] **是否清理了所有调试代码**
- [ ] **日志调用是否安全（不会阻塞业务）**

### 测试验证
- [ ] 使用API测试工具验证所有端点
- [ ] 确保返回字段都是snake_case
- [ ] 检查是否有camelCase字段泄露
- [ ] 验证异常处理是否正确

## 🔄 规则更新机制

**重要：当发现值得记录的新规则时，AI助手应自动更新此规则库**

更新原则：
1. 遇到重复性问题时，总结为规则
2. 发现最佳实践时，记录为标准
3. 修复重要bug时，提取为规范
4. 定期重构和优化规则库

示例更新场景：
- 日志系统使用规范
- 异常处理机制
- 数据库操作模式
- API设计标准
- 性能优化规则
