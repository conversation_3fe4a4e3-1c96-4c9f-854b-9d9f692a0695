<template>
  <div class="common-layout">
    <el-container>
      <!-- 左侧边栏 -->
      <SidebarMenu />
      <el-container>
        <!-- 顶部导航栏，breadcrumb通过插槽传递 -->
        <TopHeader>
          <template #breadcrumb>
            <Breadcrumb />
          </template>
        </TopHeader>
        <!-- 主内容区（替换原页签部分） -->
        <el-main>
          <TabContainer
            :key="tabStore.openedTabs.map((tab: PageConfig) => tab.name).join(',')"
          />
        </el-main>
      </el-container>
    </el-container>

    <!-- 全局抽屉组件 - 覆盖整个页面包括头部和页签 -->
    <el-drawer
      v-model="drawerStore.visible"
      :title="drawerStore.title"
      direction="rtl"
      :size="drawerStore.size"
      :modal="!drawerStore.secondVisible"
      :close-on-click-modal="true"
      :close-on-press-escape="true"
      :show-close="false"
      :destroy-on-close="true"
      :lock-scroll="false"
      class="global-drawer first-drawer"
      :z-index="10000"
    >
      <!-- 自定义头部 -->
      <template #header="">
        <DrawerHeader />
      </template>

      <!-- 抽屉内容 -->
      <div class="drawer-content-wrapper">
        <component
          v-if="drawerStore.component"
          :is="drawerStore.component"
        />

        <!-- 底部按钮 - 直接放在内容区域 -->
        <DrawerFooter />
      </div>
    </el-drawer>

    <!-- 第二层抽屉 - 从右边弹出，但位置偏移 -->
    <el-drawer
      v-model="drawerStore.secondVisible"
      :title="drawerStore.secondTitle"
      direction="rtl"
      :size="drawerStore.secondSize"
      :modal="true"
      :close-on-click-modal="true"
      :close-on-press-escape="true"
      :show-close="false"
      :destroy-on-close="true"
      :lock-scroll="false"
      class="global-drawer second-drawer"
      :z-index="10002"
      :style="secondDrawerStyle"

    >
      <!-- 自定义头部 -->
      <template #header="">
        <DrawerHeader />
      </template>

      <!-- 抽屉内容 -->
      <div class="drawer-content-wrapper">
        <component
          v-if="drawerStore.secondComponent"
          :is="drawerStore.secondComponent"
        />

        <!-- 底部按钮 - 直接放在内容区域 -->
        <DrawerFooter />
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import type { PageConfig } from '@/types/navigation';
import SidebarMenu from '@/components/layout/SidebarMenu.vue';
import TopHeader from '@/components/layout/TopHeader.vue';
import TabContainer from '@/components/layout/TabContainer.vue';
import Breadcrumb from '@/components/layout/Breadcrumb.vue';

import { useMainNavigation } from '@/composables/useMainNavigation';
import { useTabContainerStore } from '@/stores/tabContainerStore';
import { useSidebarMenuStore } from '@/stores/sidebarMenuStore';
import { useGlobalDrawerStore } from '@/stores/globalDrawerStore';
import { onMounted, computed, watch } from 'vue';

import DataSourceForm from '@/views/datasource/DataSourceForm.vue';
import InterfaceGroupForm from '@/views/interface/InterfaceGroupForm.vue';
import InterfaceTagForm from '@/views/interface/InterfaceTagForm.vue';
import InterfaceConfigForm from '@/views/interface/InterfaceConfigForm.vue';
import InterfaceVisualConfigForm from '@/views/interface/InterfaceVisualConfigForm.vue';

import ClientForm from '@/views/client/ClientForm.vue';
import ClientPermissionForm from '@/views/client/ClientPermissionForm.vue';
import ClientKeyManagement from '@/views/client/ClientKeyManagement.vue';
import ClientPermissionViewForm from '@/views/client/ClientPermissionViewForm.vue';
import ClientPermissionSettingForm from '@/views/client/ClientPermissionSettingForm.vue';
import InterfaceTestResultForm from '@/views/interface/InterfaceTestResultForm.vue';
import DrawerHeader from '@/components/common/DrawerHeader.vue';
import DrawerFooter from '@/components/common/DrawerFooter.vue';

const tabStore = useTabContainerStore();
const sidebarStore = useSidebarMenuStore();
const drawerStore = useGlobalDrawerStore();

// 计算第二层抽屉的位置
const secondDrawerStyle = computed(() => {
  const firstDrawerWidth = drawerStore.size; // 第一个抽屉的宽度
  return {
    '--first-drawer-width': firstDrawerWidth
  };
});

// 监听第二层抽屉状态，动态设置样式
watch(() => drawerStore.secondVisible, (visible) => {
  if (visible) {
    setTimeout(() => {
      const firstDrawerWidth = drawerStore.size;

      // 查找第二层抽屉
      const allDrawers = document.querySelectorAll('.el-drawer');
      let secondDrawerEl = null;
      for (const drawer of allDrawers) {
        if (drawer.classList.contains('second-drawer')) {
          secondDrawerEl = drawer;
          break;
        }
      }

      if (secondDrawerEl) {
        // 设置抽屉样式
        (secondDrawerEl as HTMLElement).style.setProperty('right', firstDrawerWidth, 'important');
        (secondDrawerEl as HTMLElement).style.setProperty('width', '45%', 'important');
        (secondDrawerEl as HTMLElement).style.setProperty('left', 'auto', 'important');
        (secondDrawerEl as HTMLElement).style.setProperty('transform', 'translateX(0)', 'important');

        // 设置遮罩层样式
        const allOverlays = document.querySelectorAll('.el-overlay');
        if (allOverlays.length > 0) {
          const secondOverlay = allOverlays[allOverlays.length - 1];
          (secondOverlay as HTMLElement).style.setProperty('right', '0', 'important');
          (secondOverlay as HTMLElement).style.setProperty('left', '0', 'important');
          (secondOverlay as HTMLElement).style.setProperty('width', '100%', 'important');
          (secondOverlay as HTMLElement).style.setProperty('height', '100vh', 'important');
          (secondOverlay as HTMLElement).style.setProperty('z-index', '10001', 'important');
          (secondOverlay as HTMLElement).style.setProperty('position', 'fixed', 'important');
        }
      }
    }, 100);
  }
});

// 调用组合式函数获取状态和方法
const { initMenuData } = useMainNavigation();

// 组件映射 - 抽屉暴露到全局供iframe使用
(window as any).globalDrawerComponents = {
  DataSourceForm,
  InterfaceGroupForm,
  InterfaceTagForm,
  InterfaceConfigForm,
  InterfaceVisualConfigForm,
  ClientForm,
  ClientPermissionForm,
  ClientKeyManagement,
  ClientPermissionViewForm,
  ClientPermissionSettingForm,
  InterfaceTestResultForm
};



// 初始化默认页签
onMounted(async () => {
  const { menuData, defaultPage } = await initMenuData();
  // 将菜单数据存入Pinia
  if (menuData) {
    sidebarStore.setMenuData(menuData);
  } else {
    console.error('未能获取有效的菜单数据');
  }
  // 使用首个菜单元素设置默认页签
  if (defaultPage) {
    tabStore.addTab(defaultPage);
  }

  // 将全局抽屉store暴露到window对象，供iframe直接访问
  (window as any).globalDrawerStore = drawerStore;
  // console.log('Global drawer store exposed:', drawerStore);
});
</script>

<style lang="scss" scoped>
/*框架布局样式 */
@use '@/assets/styles/framework.scss' as *;

/* 抽屉布局样式，自己用 - 2024-12-27 */
@use '@/assets/styles/drawer-layout.scss' as *;

/* MainIndex.vue 特有的框架布局样式 */
.common-layout {
  height: 100vh;
  @include framework-flex-column;
}

.el-container {
  flex: 1;
  display: flex;
  height: 100%;

  &:first-child {
    flex-direction: row; /* 外层容器水平排列 */
  }

  &:not(:first-child) {
    flex-direction: column; /* 内层容器垂直排列 */
  }
}

.el-main {
  padding: 0;
  overflow: auto; /* 允许内容滚动 */
  background: var(--bg-light);
  flex: 1; /* 自动填充剩余空间 */
}

/* 2024-12-27: 抽屉样式已移至drawer-layout.scss */
/*
.global-drawer {
  :deep(.el-drawer) {
    transition: transform 0.3s cubic-bezier(0.25, 0.8, 0.25, 1) !important;
  }
}

.first-drawer {
  :deep(.el-drawer) {
    z-index: 10000 !important;
  }

  :deep(.el-overlay) {
    z-index: 9999 !important;
  }
}
*/

/* 2024-12-27: 第一层抽屉样式已移至drawer-layout.scss */
/*
  :deep(.el-overlay) {
    transition: opacity 0.3s ease !important;
  }

  :deep(.el-drawer__container) {
    transition: transform 0.3s cubic-bezier(0.25, 0.8, 0.25, 1) !important;
  }

  :deep(.el-drawer__body) {
    padding: 0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    height: 100%;
    will-change: transform;
    transform: translateZ(0);
  }
*/

/* 2024-12-27: 抽屉内容包装器样式已移至drawer-layout.scss */
/*
  .drawer-content-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
    position: relative;

    > :first-child {
      flex: 1;
      overflow: auto;
      scrollbar-width: thin;
      scrollbar-color: #9db7bd #f1f5f9;

      &::-webkit-scrollbar {
        width: 4px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f5f9;
        border-radius: 2px;
      }

      &::-webkit-scrollbar-thumb {
        background: #9db7bd;
        border-radius: 2px;

        &:hover {
          background: #7a9ca3;
        }
      }
    }
  }
*/

/* 2024-12-27: 抽屉头部样式已移至drawer-layout.scss */
/*
  :deep(.el-drawer__header) {
    display: block !important;
    padding: 0 !important;
    margin: 0 !important;
    border-bottom: none !important;
  }
*/


/* 2024-12-27: 第二层抽屉样式已移至drawer-layout.scss */
/*
.second-drawer {
  :deep(.el-drawer.el-drawer--rtl.el-drawer--open) {
    right: var(--first-drawer-width, 35%) !important;
    width: 45% !important;
    left: auto !important;
    z-index: 10002 !important;
    position: fixed !important;
    top: 0 !important;
    height: 100vh !important;
    transition: transform 0.3s cubic-bezier(0.25, 0.8, 0.25, 1) !important;
    transform: translateX(0) !important;
  }

  :deep(.el-drawer.el-drawer--rtl:not(.el-drawer--open)) {
    transform: translateX(100%) !important;
    transition: transform 0.3s cubic-bezier(0.25, 0.8, 0.25, 1) !important;
  }
*/

/* 2024-12-27: 第二层抽屉样式已移至drawer-layout.scss */
/*
  :deep(.el-drawer) {
    z-index: 10002 !important;
    position: fixed !important;
    right: var(--first-drawer-width, 35%) !important;
    top: 0 !important;
    height: 100vh !important;
    width: 45% !important;
    transition: transform 0.3s cubic-bezier(0.25, 0.8, 0.25, 1) !important;
  }

  :deep(.el-drawer.rtl),
  :deep(.el-drawer[class*="rtl"]) {
    right: var(--first-drawer-width, 35%) !important;
    width: 45% !important;
    left: auto !important;
  }

  :deep(.el-overlay) {
    background: rgba(0, 0, 0, 0.3) !important;
    z-index: 10001 !important;
    position: fixed !important;
    right: 0 !important;
    left: 0 !important;
    top: 0 !important;
    width: 100% !important;
    height: 100vh !important;
    transition: opacity 0.3s ease !important;
  }
*/

/* 2024-12-27: 第二层抽屉样式已移至drawer-layout.scss */
/*
  :deep(.el-drawer__container) {
    position: fixed !important;
    right: var(--first-drawer-width, 35%) !important;
    top: 0 !important;
    width: 45% !important;
    height: 100vh !important;
    z-index: 10002 !important;
    transition: transform 0.3s cubic-bezier(0.25, 0.8, 0.25, 1) !important;
  }

  :deep(.el-drawer__body) {
    padding: 0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100% !important;
    will-change: transform;
    transform: translateZ(0);
  }
}

.second-drawer :deep(.el-drawer),
.second-drawer:deep(.el-drawer),
:deep(.second-drawer .el-drawer) {
  right: var(--first-drawer-width, 35%) !important;
  width: 45% !important;
  left: auto !important;
  position: fixed !important;
  z-index: 10002 !important;
}
*/
</style>
