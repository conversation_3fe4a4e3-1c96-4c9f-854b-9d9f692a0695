<template>
  <div class="interface-group-form drawer-form-content drawer-form drawer-select">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      label-position="left"
    >
      <el-form-item label="分组名称" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请输入分组名称，如：用户管理"
          maxlength="50"
          show-word-limit
          :disabled="loading || isView"
        />
      </el-form-item>

      <el-form-item label="分组别名" prop="pathPrefix">
        <el-input
          v-model="formData.pathPrefix"
          placeholder="请输入分组别名，如：user、order、product"
          maxlength="20"
          show-word-limit
          :disabled="loading || isView"
        />
        <div class="form-tip">
          分组别名用于API路径，只允许字母、数字、下划线、连字符，如：user → /api/v1/user/list
        </div>
      </el-form-item>

      <el-form-item label="状态" prop="isEnabled">
        <el-radio-group v-model="formData.isEnabled" :disabled="loading || isView">
          <el-radio :value="true">启用</el-radio>
          <el-radio :value="false">禁用</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="分组描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入分组描述（可选）"
          maxlength="200"
          show-word-limit
          :disabled="loading || isView"
        />
      </el-form-item>
    </el-form>
  </div>

  <!-- 底部按钮已抽象到MainIndex中统一管理 -->
</template>

<script setup lang="ts">
import { reactive, ref, computed, onMounted, watch } from 'vue';
import { ElMessage, type FormInstance } from 'element-plus';
import interfaceGroupService from '@/services/interface-group.service';
import type { InterfaceGroupRequest } from '@/types/interface-group';
import { useGlobalDrawerStore } from '@/stores/globalDrawerStore';
import { PageRefresh } from '@/utils/pageRefreshUtil';
import { extractErrorMessage } from '@/utils/common-utils';



// 全局抽屉状态管理
const drawerStore = useGlobalDrawerStore();

// 表单引用
const formRef = ref<FormInstance>();

// 加载状态
const loading = ref(false);

// 计算属性 - 从抽屉store中获取数据
const isEdit = computed(() => drawerStore.props.isEdit || false);
const isView = computed(() => drawerStore.props.isView || false);
const editData = computed(() => drawerStore.props.editData || null);

// 更新抽屉底部按钮配置
const updateDrawerButtons = () => {
  const rightButtons = [];

  if (isView.value) {
    // 查看模式：只显示关闭按钮
    rightButtons.push({
      text: '关闭',
      handler: handleCancel
    });
  } else {
    // 编辑/新增模式：显示取消和保存按钮
    rightButtons.push(
      {
        text: '取消',
        handler: handleCancel
      },
      {
        text: isEdit.value ? '更新' : '创建',
        type: 'primary' as const,
        handler: handleSubmit,
        loading: loading.value
      }
    );
  }

  // 更新store中的按钮配置
  drawerStore.leftButtons = []; // 接口分组管理没有左侧按钮
  drawerStore.rightButtons = rightButtons;
};

// 表单数据
const formData = reactive<InterfaceGroupRequest>({
  name: '',
  pathPrefix: '',
  description: '',
  isEnabled: true
});

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入分组名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  pathPrefix: [
    { required: true, message: '请输入分组别名', trigger: 'blur' },
    {
      pattern: /^[a-zA-Z0-9_-]+$/,
      message: '分组别名只允许字母、数字、下划线、连字符',
      trigger: 'blur'
    },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ]
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    loading.value = true;

    if (isEdit.value && editData.value) {
      await interfaceGroupService.updateInterfaceGroup(editData.value.id, formData);
      ElMessage.success('分组更新成功');

      // 修改操作：保持当前页刷新
      PageRefresh.interfaceGroup.afterEdit();
    } else {
      await interfaceGroupService.createInterfaceGroup(formData);
      ElMessage.success('分组创建成功');

      // 新增操作：跳转第一页刷新
      PageRefresh.interfaceGroup.afterAdd();
    }

    // 关闭抽屉
    drawerStore.closeDrawer();
  } catch (error: any) {
    console.error('保存失败:', error);
    ElMessage.error(extractErrorMessage(error, '保存失败'));
  } finally {
    loading.value = false;
  }
};

// 取消操作
const handleCancel = () => {
  drawerStore.closeDrawer();
};

// 初始化数据
const initFormData = () => {
  if ((isEdit.value || isView.value) && editData.value) {
    Object.assign(formData, {
      name: editData.value.name || '',
      pathPrefix: editData.value.pathPrefix || '',
      description: editData.value.description || '',
      isEnabled: editData.value.isEnabled !== undefined ? editData.value.isEnabled : true
    });
  } else {
    // 重置为默认值
    Object.assign(formData, {
      name: '',
      pathPrefix: '',
      description: '',
      isEnabled: true
    });
  }

  // 清除验证状态
  if (formRef.value) {
    formRef.value.clearValidate();
  }
};

// 监听抽屉store中的数据变化，确保编辑数据正确加载
watch(() => drawerStore.props, (newProps) => {
  if (newProps) {
    initFormData();
  }
  updateDrawerButtons(); // 更新按钮配置
}, { immediate: true, deep: true });



// 监听加载状态变化，更新按钮
watch([loading, isEdit], () => {
  updateDrawerButtons();
});

// 组件挂载时初始化
onMounted(() => {
  initFormData();
  updateDrawerButtons(); // 初始化按钮配置
});
</script>

<style lang="scss" scoped>
@use '@/assets/styles/page-common.scss' as *;

/* 2024-12-27: 表单内容样式已移至 page-common.scss，使用公共样式 */
// .interface-group-form {
//   /* 继承公共样式 .drawer-form-content */
// }

/* 2024-12-27: 表单提示样式已抽象到 page-common.scss，使用公共样式 */
/*
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}
*/

/* 2024-12-27: 底部按钮样式已移至 page-common.scss，使用公共样式 */

/* 2024-12-27: 下拉选择样式已移至page-common.scss的.drawer-select */
/*
:deep(.el-select) {
  width: 100%;

  .el-input {
    width: 100%;
  }

  .el-input__wrapper {
    width: 100%;
  }
}

:deep(.el-select-dropdown) {
  z-index: 25000 !important;
}

:deep(.el-popper) {
  z-index: 25000 !important;
}

:deep(.high-z-index-popper) {
  z-index: 25000 !important;
}

:deep(.el-select .el-input.is-focus .el-input__wrapper) {
  box-shadow: 0 0 0 1px #409eff inset;
}
*/
</style>
