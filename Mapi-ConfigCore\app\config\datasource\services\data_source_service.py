"""
数据源业务逻辑层
处理数据源相关的业务逻辑
"""

import time
from typing import List, Optional
from sqlalchemy.orm import Session
from app.config.datasource.repositories.data_source_repository import DataSourceRepository
from app.config.datasource.schemas.data_source_schema import (
    DataSourceCreate,  # 数据源创建模型
    DataSourceUpdate,  # 数据源更新模型
    DataSourceResponse,  # 数据源响应模型
    DataSourceListResponse,  # 数据源列表响应模型
    ConnectionTestResponse  # 连接测试响应模型
)
from app.shared.utils.connection_tester import ConnectionTester
from app.shared.core.business_response import BusinessResponse
from app.shared.core.base_response import ErrorType
from app.shared.core.log_util import LogUtil
from app.shared.crypto_utils import decrypt_password
import math
import time

class DataSourceService:
    """数据源业务逻辑类"""
    
    def __init__(self, db: Session):
        self.db = db
        self.repository = DataSourceRepository(db)
        self.connection_tester = ConnectionTester()
    
    def get_data_sources(
        self, 
        page: int = 1, 
        size: int = 10, 
        search: Optional[str] = None,
        status: Optional[str] = None,
        db_type: Optional[str] = None
    ) -> DataSourceListResponse:
        """
        获取数据源列表
        
        Args:
            page: 页码
            size: 每页大小
            search: 搜索关键词
            status: 状态过滤
            db_type: 数据库类型过滤
            
        Returns:
            数据源列表响应
        """
        try:
            # 参数验证
            if page < 1:
                raise BusinessException(
                    user_message="页码必须大于0",
                    user_detail={"page": page},
                    error_type=ErrorType.验证错误
                )
            
            if size < 1 or size > 100:
                raise BusinessException(
                    user_message="每页大小必须在1-100之间",
                    user_detail={"size": size},
                    error_type=ErrorType.验证错误
                )
            
            # 获取数据
            items, total = self.repository.get_list(page, size, search, status, db_type)
            
            # 转换为响应格式
            data_sources = []
            for item in items:
                # 使用标准的Pydantic模型
                data_sources.append(DataSourceResponse.from_orm(item))
            
            # 计算总页数
            pages = math.ceil(total / size) if total > 0 else 0
            
            return DataSourceListResponse(
                items=data_sources,
                total=total,
                page=page,
                size=size,
                pages=pages
            )
            
        except Exception as e:
            raise TechnicalException(
                error_type=ErrorType.数据库错误,
                developer_detail={
                    "operation": "查询数据源列表",
                    "error_detail": str(e),
                    "query_params": {
                        "page": page,
                        "size": size,
                        "search": search,
                        "status": status,
                        "db_type": db_type
                    },
                    "business_context": "用户请求数据源列表时发生数据库查询异常"
                }
            )
    
    def get_data_source(self, data_source_id: int) -> DataSourceResponse:
        """
        获取单个数据源
        
        Args:
            data_source_id: 数据源ID
            
        Returns:
            数据源响应
        """
        try:
            data_source = self.repository.get_by_id(data_source_id)
            if not data_source:
                raise BusinessException(
                    user_message="数据源不存在",
                    user_detail={"id": data_source_id},
                    error_type=ErrorType.资源未找到
                )
            
            # 转换为响应格式
            return DataSourceResponse.from_orm(data_source)
            
        except Exception as e:
            raise TechnicalException(
                error_type=ErrorType.数据库错误,
                developer_detail={
                    "operation": "根据ID查询单个数据源",
                    "error_detail": str(e),
                    "target_resource": {
                        "data_source_id": data_source_id,
                        "resource_type": "DataSource"
                    },
                    "business_context": f"用户请求查看ID为{data_source_id}的数据源详情时发生数据库查询异常"
                }
            )
    
    def create_data_source(self, data_source_data: DataSourceCreate) -> DataSourceResponse:
        """
        创建数据源
        
        Args:
            data_source_data: 数据源创建数据
            
        Returns:
            创建的数据源响应
        """
        try:
            # 检查名称是否已存在
            if self.repository.check_name_exists(data_source_data.name):
                raise BusinessException(
                    user_message="数据源名称已存在",
                    user_detail={
                        "name": data_source_data.name,
                        "suggestion": "请使用其他名称"
                    },
                    error_type=ErrorType.资源冲突
                )
            
            # 创建数据源
            data_source = self.repository.create(data_source_data)
            
            # 转换为响应格式
            return DataSourceResponse.from_orm(data_source)
            
        except Exception as e:
            raise TechnicalException(
                error_type=ErrorType.数据库错误,
                developer_detail={
                    "operation": "创建新数据源",
                    "error_detail": str(e),
                    "create_data": {
                        "name": data_source_data.name,
                        "db_type": data_source_data.db_type,
                        "host": data_source_data.host,
                        "port": data_source_data.port,
                        "database": data_source_data.database
                    },
                    "business_context": f"用户创建名为'{data_source_data.name}'的{data_source_data.db_type}数据源时发生数据库写入异常"
                }
            )
    
    def update_data_source(self, data_source_id: int, data_source_data: DataSourceUpdate) -> DataSourceResponse:
        """
        更新数据源
        
        Args:
            data_source_id: 数据源ID
            data_source_data: 更新数据
            
        Returns:
            更新后的数据源响应
        """
        try:
            # 检查数据源是否存在
            existing_data_source = self.repository.get_by_id(data_source_id)
            if not existing_data_source:
                raise BusinessException(
                    user_message="数据源不存在",
                    user_detail={"id": data_source_id},
                    error_type=ErrorType.资源未找到
                )
            
            # 如果更新名称，检查是否重复
            if data_source_data.name and self.repository.check_name_exists(data_source_data.name, data_source_id):
                raise BusinessException(
                    user_message="数据源名称已存在",
                    user_detail={
                        "name": data_source_data.name,
                        "suggestion": "请使用其他名称"
                    },
                    error_type=ErrorType.资源冲突
                )
            
            # 更新数据源
            updated_data_source = self.repository.update(data_source_id, data_source_data)
            
            # 转换为响应格式
            return DataSourceResponse.from_orm(updated_data_source)
            
        except Exception as e:
            raise TechnicalException(
                error_type=ErrorType.数据库错误,
                developer_detail={
                    "operation": "更新数据源信息",
                    "error_detail": str(e),
                    "update_target": {
                        "data_source_id": data_source_id,
                        "update_fields": list(data_source_data.dict(exclude_unset=True).keys())
                    },
                    "business_context": f"用户更新ID为{data_source_id}的数据源时发生数据库更新异常"
                }
            )
    
    def delete_data_source(self, data_source_id: int) -> dict:
        """
        删除数据源
        
        Args:
            data_source_id: 数据源ID
            
        Returns:
            删除结果
        """
        LogUtil.info("🗑️ 开始删除数据源", data_source_id=data_source_id)

        try:
            # 检查数据源是否存在
            existing_data_source = self.repository.get_by_id(data_source_id)
            if not existing_data_source:
                return BusinessResponse.handle_resource_not_found(
                    resource_type="数据源",
                    resource_id=data_source_id
                )
            
            # 检查是否有接口在使用此数据源
            try:
                from app.config.interface.repositories.interface_config_repository import InterfaceConfigRepository
                interface_repo = InterfaceConfigRepository(self.db)

                # 查询使用此数据源的接口配置
                related_configs = interface_repo.get_by_datasource_id(data_source_id)
                if related_configs:
                    interface_names = [config.name for config in related_configs]

                    # 使用业务响应处理器处理资源冲突
                    return BusinessResponse.handle_resource_conflict(
                        resource_type="数据源",
                        resource_id=data_source_id,
                        conflict_reason="仍有接口配置在使用",
                        related_items=interface_names,
                        suggestion="请先删除或修改相关的接口配置"
                    )
            except ImportError as e:
                # 如果接口管理模块不可用，记录警告但继续删除
                LogUtil.warning("接口管理模块不可用，跳过关联检查", error=str(e))
            except BusinessException:
                # 重新抛出业务异常（如关联检查失败）
                raise
            except Exception as e:
                # 如果检查过程中出现其他错误，记录错误但继续删除
                LogUtil.tech_error("检查接口关联时发生错误", error=str(e))
                # 为了安全起见，如果检查失败，阻止删除
                from app.shared.core.exception_handler import BusinessException
                raise BusinessException(
                    user_message="删除前检查失败，为了数据安全，删除操作已取消",
                    user_detail={
                        "datasource_id": data_source_id,
                        "check_error": str(e),
                        "suggestion": "请联系管理员检查系统状态"
                    },
                    error_type=ErrorType.未知错误
                )
            
            # 删除数据源
            success = self.repository.delete(data_source_id)
            
            if success:
                return BusinessResponse.handle_success_operation(
                    operation="删除数据源",
                    resource_type="数据源",
                    resource_id=data_source_id,
                    data={"id": data_source_id}
                )
            else:
                # 这里应该使用异常处理，因为删除失败是技术问题
                from app.shared.core.exception_handler import TechnicalException
                raise TechnicalException(
                    error_type=ErrorType.数据库错误,
                    developer_detail={
                        "operation": "delete_data_source",
                        "error": "删除操作失败",
                        "data_source_id": data_source_id
                    }
                )
                
        except Exception as e:
            from app.shared.core.exception_handler import TechnicalException
            raise TechnicalException(
                error_type=ErrorType.数据库错误,
                developer_detail={
                    "operation": "删除数据源",
                    "error_detail": str(e),
                    "delete_target": {
                        "data_source_id": data_source_id,
                        "resource_type": "DataSource"
                    },
                    "business_context": f"用户删除ID为{data_source_id}的数据源时发生数据库删除异常"
                }
            )
    


    async def test_saved_data_source_connection(self, data_source_id: int) -> ConnectionTestResponse:
        """
        测试已保存数据源的连接

        Args:
            data_source_id: 数据源ID

        Returns:
            连接测试响应
        """
        try:
            # 获取数据源信息
            data_source = self.repository.get_by_id(data_source_id)
            if not data_source:
                raise BusinessException(
                    user_message="数据源不存在",
                    user_detail={"id": data_source_id},
                    error_type=ErrorType.资源未找到
                )

            start_time = time.time()

            # 解密密码
            try:
                decrypted_password = decrypt_password(data_source.password)
            except Exception as e:
                raise TechnicalException(
                    error_type=ErrorType.加密错误,
                    developer_detail={
                        "operation": "decrypt_password",
                        "error": str(e),
                        "data_source_id": data_source_id
                    }
                )

            # 执行连接测试 - 使用较短的超时时间以提升用户体验
            # 测试连接时使用10秒超时，而不是数据源配置的超时时间
            test_timeout = min(10, data_source.connection_timeout)  # 最多10秒
            result = self.connection_tester.test_connection(
                db_type=data_source.db_type,
                host=data_source.host,
                port=data_source.port,
                database=data_source.database,
                username=data_source.username,
                password=decrypted_password,
                timeout=test_timeout
            )

            end_time = time.time()
            response_time = round((end_time - start_time) * 1000, 2)  # 转换为毫秒

            return ConnectionTestResponse(
                success=result.success,
                message=result.message,
                response_time=response_time,
                error_detail=result.error_detail
            )

        except Exception as e:
            raise TechnicalException(
                error_type=ErrorType.连接错误,
                developer_detail={
                    "operation": "test_saved_data_source_connection",
                    "error": str(e),
                    "data_source_id": data_source_id
                }
            )

    async def validate_table_name(self, data_source_id: int, table_name: str, table_type: str) -> dict:
        """
        校验表/视图/存储过程名称是否存在

        Args:
            data_source_id: 数据源ID
            table_name: 表/视图/存储过程名称
            table_type: 类型（table/view/procedure）

        Returns:
            校验结果
        """
        try:
            LogUtil.info("开始校验表名称",
                        operation="validate_table_name",
                        data_source_id=data_source_id,
                        table_name=table_name,
                        table_type=table_type)

            # 获取数据源信息
            data_source = self.repository.get_by_id(data_source_id)
            if not data_source:
                return {
                    "success": False,
                    "message": "数据源不存在",
                    "exists": False
                }

            # 解密密码
            decrypted_password = decrypt_password(data_source.password)

            # 构建连接参数
            connection_params = {
                "db_type": data_source.db_type,
                "host": data_source.host,
                "port": data_source.port,
                "database": data_source.database,
                "username": data_source.username,
                "password": decrypted_password
            }

            # 使用连接测试器校验表名称
            validation_result = await self.connection_tester.validate_table_name(
                connection_params, table_name, table_type
            )

            LogUtil.info("表名称校验完成",
                        operation="validate_table_name",
                        data_source_id=data_source_id,
                        table_name=table_name,
                        table_type=table_type,
                        exists=validation_result.get("exists", False))

            return {
                "success": True,
                "message": f"{'存在' if validation_result.get('exists') else '不存在'}",
                "exists": validation_result.get("exists", False),
                "table_type": table_type,
                "table_name": table_name
            }

        except Exception as e:
            LogUtil.error("表名称校验失败",
                         operation="validate_table_name",
                         error=str(e),
                         data_source_id=data_source_id,
                         table_name=table_name,
                         table_type=table_type)

            return {
                "success": False,
                "message": f"校验失败: {str(e)}",
                "exists": False
            }

    async def get_table_structure(self, data_source_id: int, table_name: str, table_type: str) -> dict:
        """
        获取表结构信息

        Args:
            data_source_id: 数据源ID
            table_name: 表名称
            table_type: 类型（table/view）

        Returns:
            表结构信息
        """
        try:
            LogUtil.info("开始获取表结构",
                        operation="get_table_structure",
                        data_source_id=data_source_id,
                        table_name=table_name,
                        table_type=table_type)

            # 获取数据源信息
            data_source = self.repository.get_by_id(data_source_id)
            if not data_source:
                return {
                    "success": False,
                    "message": "数据源不存在",
                    "columns": []
                }

            # 解密密码
            decrypted_password = decrypt_password(data_source.password)

            # 构建连接参数
            connection_params = {
                "db_type": data_source.db_type,
                "host": data_source.host,
                "port": data_source.port,
                "database": data_source.database,
                "username": data_source.username,
                "password": decrypted_password
            }

            # 使用连接测试器获取表结构
            structure_result = await self.connection_tester.get_table_structure(
                connection_params, table_name, table_type
            )

            LogUtil.info("表结构获取完成",
                        operation="get_table_structure",
                        data_source_id=data_source_id,
                        table_name=table_name,
                        table_type=table_type,
                        column_count=len(structure_result.get("columns", [])))

            return structure_result

        except Exception as e:
            LogUtil.error("表结构获取失败",
                         operation="get_table_structure",
                         error=str(e),
                         data_source_id=data_source_id,
                         table_name=table_name,
                         table_type=table_type)

            return {
                "success": False,
                "message": f"获取表结构失败: {str(e)}",
                "columns": []
            }


