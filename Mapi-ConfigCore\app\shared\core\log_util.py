import logging
import json
import inspect
import os

class LogUtil:
    """保持最小化接口，只实现必须的方法"""

    @staticmethod
    def _get_caller_info(stack_level=3):
        """自动获取调用者信息"""
        try:
            frame = inspect.stack()[stack_level]
            filename = os.path.basename(frame.filename)  # 只取文件名，不要完整路径
            function_name = frame.function
            line_number = frame.lineno
            return {
                "file": filename,
                "method": function_name,
                "line": line_number
            }
        except:
            return {
                "file": "unknown",
                "method": "unknown",
                "line": 0
            }

    @staticmethod
    def _format_extra_data(kwargs, caller_info=None):
        """格式化额外数据为可读字符串"""
        if not kwargs and not caller_info:
            return ""

        # 合并调用者信息和额外数据
        all_data = {}
        if caller_info:
            all_data.update(caller_info)
        if kwargs:
            all_data.update(kwargs)

        try:
            # 如果数据较多，使用多行格式
            if len(all_data) > 3 or any(len(str(v)) > 50 for v in all_data.values()):
                formatted_items = []
                for key, value in all_data.items():
                    if isinstance(value, str) and len(value) > 50:
                        formatted_items.append(f"\n  {key}: {value}")
                    else:
                        formatted_items.append(f"\n  {key}: {value}")
                return "".join(formatted_items)
            else:
                return " | " + json.dumps(all_data, ensure_ascii=False, separators=(', ', ': '))
        except:
            return " | " + str(all_data)

    @staticmethod
    def _get_logger(log_type: str):
        """获取指定类型的记录器"""
        from app.shared.core.log_init import setup_logging
        if not hasattr(setup_logging, '_loggers'):
            setup_logging()
        return setup_logging._loggers.get(log_type)

    # 异常记录方法（由异常处理系统调用）
    @staticmethod
    def biz_info(message: str, **kwargs):
        """记录业务异常信息，只写入biz_*.log"""
        caller_info = LogUtil._get_caller_info(2)
        full_message = message + LogUtil._format_extra_data(kwargs, caller_info)
        logger = LogUtil._get_logger('biz')
        if logger:
            logger.info(full_message)



    # 调试日志接口 - 严格按级别分类，各自独立
    @staticmethod
    def debug(message: str, **kwargs):
        """调试级别日志，只写入debug_*.log"""
        try:
            caller_info = LogUtil._get_caller_info(2)
            full_message = message + LogUtil._format_extra_data(kwargs, caller_info)
            logger = LogUtil._get_logger('debug')
            if logger:
                logger.debug(full_message)
        except Exception:
            # 日志错误不能阻塞业务流程，静默忽略
            pass

    @staticmethod
    def info(message: str, **kwargs):
        """信息级别日志，只写入info_*.log"""
        try:
            caller_info = LogUtil._get_caller_info(2)
            full_message = message + LogUtil._format_extra_data(kwargs, caller_info)
            logger = LogUtil._get_logger('info')
            if logger:
                logger.info(full_message)
        except Exception:
            # 日志错误不能阻塞业务流程，静默忽略
            pass

    @staticmethod
    def warning(message: str, **kwargs):
        """警告级别日志，只写入warning_*.log"""
        try:
            caller_info = LogUtil._get_caller_info(2)
            full_message = message + LogUtil._format_extra_data(kwargs, caller_info)
            logger = LogUtil._get_logger('warning')
            if logger:
                logger.warning(full_message)
        except Exception:
            # 日志错误不能阻塞业务流程，静默忽略
            pass

    @staticmethod
    def error(message: str, **kwargs):
        """错误级别日志，只写入error_*.log"""
        try:
            caller_info = LogUtil._get_caller_info(2)
            full_message = message + LogUtil._format_extra_data(kwargs, caller_info)
            logger = LogUtil._get_logger('error')
            if logger:
                logger.error(full_message)
        except Exception:
            # 日志错误不能阻塞业务流程，静默忽略
            pass

    @staticmethod
    def tech_error(message: str, **kwargs):
        """技术错误日志，只写入tech_error_*.log"""
        try:
            # 临时禁用日志记录，确保不阻塞业务流程
            # caller_info = LogUtil._get_caller_info(2)
            # full_message = message + LogUtil._format_extra_data(kwargs, caller_info)
            # logger = LogUtil._get_logger('tech_error')
            # if logger:
            #     logger.error(full_message)
            pass
        except Exception:
            # 日志错误不能阻塞业务流程，静默忽略
            pass
