/**
 * 通用工具类
 * 包含字段转换、密码处理、错误处理等前后端通用的工具方法
 */

/**
 * 将下划线格式转换为驼峰格式
 * 例如：user_name -> userName, created_at -> createdAt
 *
 * @param str 下划线格式的字符串
 * @returns 驼峰格式的字符串
 */
export const toCamelCase = (str: string): string => {
  return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
};

/**
 * 将驼峰格式转换为下划线格式
 * 例如：userName -> user_name, createdAt -> created_at
 *
 * @param str 驼峰格式的字符串
 * @returns 下划线格式的字符串
 */
export const toSnakeCase = (str: string): string => {
  return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
};

/**
 * 递归转换对象的所有键为驼峰格式
 *
 * @param obj 要转换的对象
 * @returns 转换后的对象
 */
export const convertToCamelCase = <T = any>(obj: any): T => {
  if (obj === null || obj === undefined) {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(item => convertToCamelCase(item)) as T;
  }

  if (typeof obj === 'object' && obj.constructor === Object) {
    const converted: any = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        const camelKey = toCamelCase(key);
        converted[camelKey] = convertToCamelCase(obj[key]);
      }
    }
    return converted;
  }

  return obj;
};

/**
 * 递归转换对象的所有键为下划线格式
 *
 * @param obj 要转换的对象
 * @returns 转换后的对象
 */
export const convertToSnakeCase = <T = any>(obj: any): T => {
  if (obj === null || obj === undefined) {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(item => convertToSnakeCase(item)) as T;
  }

  if (typeof obj === 'object' && obj.constructor === Object) {
    const converted: any = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        const snakeKey = toSnakeCase(key);
        converted[snakeKey] = convertToSnakeCase(obj[key]);
      }
    }
    return converted;
  }

  return obj;
};

/**
 * API请求数据转换装饰器
 * 自动将前端驼峰格式转换为后端下划线格式
 */
export const apiRequest = <T extends any[], R>(
  apiFunction: (...args: T) => Promise<R>
) => {
  return async (...args: T): Promise<R> => {
    // 转换请求参数
    const convertedArgs = args.map(arg =>
      typeof arg === 'object' && arg !== null ? convertToSnakeCase(arg) : arg
    ) as T;

    const result = await apiFunction(...convertedArgs);

    // 转换响应数据
    return convertToCamelCase(result);
  };
};

/**
 * 批量转换对象数组
 *
 * @param items 对象数组
 * @param converter 转换函数
 * @returns 转换后的数组
 */
export const convertArray = <T>(
  items: T[],
  converter: (item: T) => T
): T[] => {
  return items.map(converter);
};

/**
 * 深度克隆并转换对象
 *
 * @param obj 源对象
 * @param converter 转换函数
 * @returns 转换后的新对象
 */
export const deepConvert = <T>(obj: T, converter: (obj: T) => T): T => {
  return converter(JSON.parse(JSON.stringify(obj)));
};

/**
 * 日期时间工具类
 * 用于日期时间的格式化和转换
 */
export class DateTimeUtils {
  /**
   * 格式化日期时间为标准格式
   * @param dateTime 日期时间字符串或Date对象
   * @param format 格式类型，默认为 'datetime'
   * @returns 格式化后的字符串
   */
  static formatDateTime(
    dateTime: string | Date | null | undefined,
    format: 'datetime' | 'date' | 'time' | 'relative' = 'datetime'
  ): string {
    if (!dateTime) {
      return '-';
    }

    const date = typeof dateTime === 'string' ? new Date(dateTime) : dateTime;

    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      return '-';
    }

    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    switch (format) {
      case 'datetime':
        return date.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
          hour12: false
        });

      case 'date':
        return date.toLocaleDateString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit'
        });

      case 'time':
        return date.toLocaleTimeString('zh-CN', {
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
          hour12: false
        });

      case 'relative':
        if (diffDays === 0) {
          return '今天';
        } else if (diffDays === 1) {
          return '昨天';
        } else if (diffDays < 7) {
          return `${diffDays}天前`;
        } else if (diffDays < 30) {
          const weeks = Math.floor(diffDays / 7);
          return `${weeks}周前`;
        } else if (diffDays < 365) {
          const months = Math.floor(diffDays / 30);
          return `${months}个月前`;
        } else {
          const years = Math.floor(diffDays / 365);
          return `${years}年前`;
        }

      default:
        return date.toLocaleString('zh-CN');
    }
  }

  /**
   * 格式化为友好的相对时间
   * @param dateTime 日期时间
   * @returns 相对时间字符串
   */
  static formatRelativeTime(dateTime: string | Date | null | undefined): string {
    return this.formatDateTime(dateTime, 'relative');
  }

  /**
   * 格式化为标准日期时间
   * @param dateTime 日期时间
   * @returns 标准格式的日期时间字符串
   */
  static formatStandardDateTime(dateTime: string | Date | null | undefined): string {
    return this.formatDateTime(dateTime, 'datetime');
  }

  /**
   * 格式化为日期
   * @param dateTime 日期时间
   * @returns 日期字符串
   */
  static formatDate(dateTime: string | Date | null | undefined): string {
    return this.formatDateTime(dateTime, 'date');
  }

  /**
   * 格式化为时间
   * @param dateTime 日期时间
   * @returns 时间字符串
   */
  static formatTime(dateTime: string | Date | null | undefined): string {
    return this.formatDateTime(dateTime, 'time');
  }

  /**
   * 检查日期是否为今天
   * @param dateTime 日期时间
   * @returns 是否为今天
   */
  static isToday(dateTime: string | Date | null | undefined): boolean {
    if (!dateTime) return false;

    const date = typeof dateTime === 'string' ? new Date(dateTime) : dateTime;
    const today = new Date();

    return date.toDateString() === today.toDateString();
  }

  /**
   * 获取时间差描述
   * @param startTime 开始时间
   * @param endTime 结束时间，默认为当前时间
   * @returns 时间差描述
   */
  static getTimeDifference(
    startTime: string | Date,
    endTime: string | Date = new Date()
  ): string {
    const start = typeof startTime === 'string' ? new Date(startTime) : startTime;
    const end = typeof endTime === 'string' ? new Date(endTime) : endTime;

    const diffMs = end.getTime() - start.getTime();
    const diffSeconds = Math.floor(diffMs / 1000);
    const diffMinutes = Math.floor(diffSeconds / 60);
    const diffHours = Math.floor(diffMinutes / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffDays > 0) {
      return `${diffDays}天`;
    } else if (diffHours > 0) {
      return `${diffHours}小时`;
    } else if (diffMinutes > 0) {
      return `${diffMinutes}分钟`;
    } else {
      return `${diffSeconds}秒`;
    }
  }
}

/**
 * 密码工具类
 * 用于密码相关的处理
 */
export class PasswordUtils {
  /**
   * 处理数据源对象的密码字段
   * 直接返回原数据，前端显示加密密码，后端负责智能处理
   *
   * @param dataSource 数据源对象
   * @returns 处理后的数据源对象（保持原密码）
   */
  static processDataSourcePassword(dataSource: any): any {
    if (!dataSource) {
      return dataSource;
    }

    // 直接返回原数据，包括加密的密码
    // 前端显示加密密码，后端负责比对和加密逻辑
    return { ...dataSource };
  }
}

/**
 * 智能提取错误消息
 * 避免显示 [object Object] 错误
 *
 * @param error 错误对象
 * @param defaultMessage 默认错误消息
 * @returns 可读的错误消息
 */
export const extractErrorMessage = (error: any, defaultMessage: string = '操作失败'): string => {
  // 开发环境调试信息
  // if (process.env.NODE_ENV === 'development') {
  //   console.log('🔍 extractErrorMessage 接收到的错误:', error);
  //   console.log('🔍 错误类型:', typeof error);
  //   console.log('🔍 错误构造函数:', error?.constructor?.name);
  // }

  // 如果是字符串，直接返回
  if (typeof error === 'string') {
    return error;
  }

  // 如果是标准Error对象，优先使用message
  if (error?.message && typeof error.message === 'string') {
    return error.message;
  }

  // 如果是API响应错误（增强的错误对象）
  if (error?.response?.data?.message) {
    return error.response.data.message;
  }

  // 如果是业务错误
  if (error?.detail && typeof error.detail === 'string') {
    return error.detail;
  }

  // 如果detail是对象，尝试提取message
  if (error?.detail?.message) {
    return error.detail.message;
  }

  // 如果是HTTP错误
  if (error?.status && error?.statusText) {
    return `${error.status}: ${error.statusText}`;
  }

  // 如果错误对象有toString方法且不是默认的[object Object]
  if (error && typeof error.toString === 'function') {
    const stringified = error.toString();
    if (stringified !== '[object Object]') {
      return stringified;
    }
  }

  // 兜底返回默认消息
  return defaultMessage;
};
