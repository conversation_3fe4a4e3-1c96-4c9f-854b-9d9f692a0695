"""
接口配置数据模型
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, ForeignKey, func
from sqlalchemy.orm import relationship
from app.shared.database import Base


class InterfaceConfigModel(Base):
    """接口配置表模型"""
    
    __tablename__ = "interface_configs"
    
    # 主键
    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    
    # 基本信息
    name = Column(String(100), nullable=False, comment="接口名称")
    path = Column(String(200), nullable=False, unique=True, comment="接口路径")
    method = Column(String(10), nullable=False, comment="HTTP方法")
    description = Column(Text, nullable=True, comment="接口描述")
    
    # 关联信息
    group_id = Column(Integer, ForeignKey('interface_groups.id'), nullable=False, comment="所属分组ID")
    datasource_id = Column(Integer, ForeignKey('data_sources.id'), nullable=False, comment="数据源ID")
    table_name = Column(String(100), nullable=False, comment="数据表名")
    table_type = Column(String(20), nullable=False, default='table', comment="数据表类型(table/view/procedure)")
    
    # 配置信息
    is_enabled = Column(Boolean, nullable=False, default=True, comment="是否启用")
    is_public = Column(Boolean, nullable=False, default=False, comment="是否公开(无需认证)")
    query_fields = Column(Text, nullable=True, comment="可查询字段(JSON格式)")
    required_fields = Column(Text, nullable=True, comment="必填字段(JSON格式)")
    response_fields = Column(Text, nullable=True, comment="响应字段(JSON格式)")

    # ORM模型配置
    orm_model_config = Column(Text, nullable=True, comment="ORM模型配置(JSON格式)")
    orm_model_name = Column(String(100), nullable=True, comment="ORM模型名称")
    orm_relationships = Column(Text, nullable=True, comment="ORM关联关系配置(JSON格式)")

    # 参数配置
    parameter_config = Column(Text, nullable=True, comment="参数配置(JSON格式)")
    visual_config = Column(Text, nullable=True, comment="可视化配置(JSON格式)")
    validation_rules = Column(Text, nullable=True, comment="验证规则配置(JSON格式)")
    
    # 性能配置
    cache_duration = Column(Integer, nullable=False, default=300, comment="缓存时长(秒)")
    rate_limit = Column(Integer, nullable=False, default=100, comment="速率限制(次/分钟)")
    
    # 测试信息
    last_test_at = Column(DateTime, nullable=True, comment="最后测试时间")
    test_status = Column(String(20), nullable=True, comment="测试状态(success/failed)")
    test_data = Column(Text, nullable=True, comment="完整测试数据(JSON格式)")
    
    # 审计字段
    created_at = Column(DateTime, nullable=False, default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, nullable=False, default=func.now(), onupdate=func.now(), comment="更新时间")
    created_by = Column(String(50), nullable=True, comment="创建人")
    
    # 关联关系 - 使用字符串引用避免循环导入
    group = relationship("InterfaceGroupModel", backref="interfaces", lazy="select")
    datasource = relationship("DataSourceModel", backref="interfaces", lazy="select")
    
    def __repr__(self):
        return f"<InterfaceConfig(id={self.id}, name='{self.name}', path='{self.path}', method='{self.method}')>"
    

